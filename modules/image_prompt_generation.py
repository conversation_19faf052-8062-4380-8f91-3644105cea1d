# utils/image_prompt_generation.py
"""
图像生成提示词相关功能模块

此模块包含与图像提示词生成相关的函数，使用LangChain的LCEL实现
多步骤链式处理，解决风格一致性和角色统一的问题。
"""

import json
import logging
import base64 # Added for scene graph encoding
import hashlib # Added for seed generation
import time # Added for seed generation
from dataclasses import dataclass, field # Added for SegmentCtx
# Attempt to import settings, handle potential ImportError
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

class DefaultSettings:
    srl_confidence_threshold = 0.6
    grounding_cosine_threshold = 0.55 # Legacy, consider removing or using for initial check
    grounding_min_score = 0.50 # Minimum acceptable cross-encoder score
    srl_fallback_token_limit = 50
    use_fpa = True
    fpa_batch_size = 8 # Added default batch size
# Add other settings as needed
settings = DefaultSettings()

from modules.langchain_interface import call_llm_json_response, call_llm # Added call_llm import
# --- NLP Scene Imports ---# --- New Imports ---
from modules.nlp_scene import hybrid_scene_graph, classify_scene_type, cross_encoder_score, log_grounding_failure, SPACY_NLP # Import new functions and SPACY_NLP
# --- FPA Import (optional) --- REMOVED
from modules.safety_filter import apply_gore_neutralization, check_azure_content_filter # Import the new function

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain.memory import SimpleMemory
from langchain.schema.runnable import RunnableSequence, RunnableLambda
from pydantic import BaseModel
import re
from typing import Dict, Any, List, Optional, Union, Tuple, Iterator

# ============ Helper dataclasses ============
@dataclass
class SegmentCtx:
    seg_id: int
    raw_text: str
    sanitized_text: str = ""
    gore_flag: bool = False
    scene_graph: dict = field(default_factory=dict)
    initial_scene_type: str = ""
    processed_prompts: list = field(default_factory=list) # Changed from 'prompts' to 'processed_prompts' for clarity
    error: Optional[str] = None
    symbolic_action: str = "" # Added field to store symbolic action

@dataclass
class GlobalContext:
    visual_style: str = ""
    style_details: dict = field(default_factory=dict)
    all_characters: list = field(default_factory=list)
    time_period: str = ""
    simplified_characters: list = field(default_factory=list)

# 添加 Pydantic 模型定义
class StoryInputSchema(BaseModel):
    text: str
    segment_text: str
    num_images: int
    prompt_suffix: str

class StoryOutputSchema(BaseModel):
    visual_style: str
    style_details: Dict[str, Any]
    all_characters: List[Dict[str, Any]]
    image_prompt: Dict[str, Any]

# ============ Existing Helper Functions (enhance_prompts_with_character_info, _create_description_from_descriptor) ============
# ... (rest of the functions remain here) ...
# 新增：程序化匹配和增强提示词中的角色信息
def enhance_prompts_with_character_info(
    image_prompt: Dict[str, Any],
    all_characters: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    程序化地增强提示词中的角色信息 (使用预构建的 character_map)。

    Args:
        image_prompt: 图像提示词
        all_characters: 所有角色信息

    Returns:
        增强后的图像提示词
    """
    if not all_characters or not image_prompt or 'prompts' not in image_prompt:
        return image_prompt

    # Handling potential missing keys more gracefully
    for prompt_item in image_prompt.get('prompts', []):
        if not isinstance(prompt_item, dict):
            continue

        scene_type = prompt_item.get('scene_type', 'concrete')

        # Ensure 'blocks' and 'characters' exist before proceeding
        if 'blocks' in prompt_item:
            # Ensure characters list exists, even if empty
            prompt_item.setdefault('characters', [])

            if scene_type == "abstract":
                prompt_item['characters'] = [] # Ensure abstract scenes have no characters

            characters = prompt_item.get('characters', []) # Use .get for safety

            for i, character in enumerate(characters):
                if not isinstance(character, dict):
                    continue
                # Lookup character info by ID
                char_id = character.get('character_id')
                if not char_id:
                    continue
                # Find the full character info in all_characters
                char_info = next((c for c in all_characters if c.get('character_id') == char_id), None)
                if not char_info:
                    character.pop('descriptor', None)
                    continue

                # Build descriptor text
                descriptor_text = None
                clothing_text = None

                if 'descriptor' in char_info:
                    desc = char_info['descriptor']
                    if isinstance(desc, dict) and 'description' in desc:
                        descriptor_text = desc['description']
                    else:
                        result = _create_description_from_descriptor(desc)
                        if isinstance(result, tuple):
                            descriptor_text, clothing_text = result
                        else:
                            descriptor_text = result
                elif 'appearance' in char_info:
                    app = char_info['appearance']
                    # 收集核心外观特征（不包括服装）
                    core_parts = []

                    if app.get('ethnicity'):
                        core_parts.append(app.get('ethnicity'))

                    # 添加年龄阶段信息
                    if app.get('age_stage'):
                        core_parts.append(app.get('age_stage'))

                    if app.get('body_type'):
                        body_type = app.get('body_type')
                        if 'build' not in body_type.lower():
                            body_type += " build"
                        core_parts.append(body_type)

                    if app.get('hair'):
                        core_parts.append(f"hair {app.get('hair')}")

                    if app.get('eyes'):
                        core_parts.append(f"eyes {app.get('eyes')}")

                    if app.get('distinctive_features'):
                        core_parts.append(app.get('distinctive_features'))

                    descriptor_text = ", ".join(core_parts) if core_parts else None

                    # 单独处理服装
                    if app.get('typical_clothing'):
                        clothing_text = f"wearing {app.get('typical_clothing')}"

                canonical_name = char_info.get('character_name', char_id)
                if descriptor_text:
                    # 使用括号而不是方括号，更紧凑的格式
                    character['descriptor'] = f"{canonical_name} ({descriptor_text})"
                    # 存储服装信息，以便在后续处理中使用
                    if clothing_text:
                        character['clothing'] = clothing_text
                else:
                    character.pop('descriptor', None)

        # Handle potential old format (less likely with new prompt structure)
        elif 'prompt' in prompt_item:
             logger.debug("Handling legacy prompt format in enhance_prompts_with_character_info.")
             # ... (keep existing legacy handling if necessary, but it might become obsolete) ...
             pass # Keep legacy handling for now if needed

    return image_prompt

def _create_description_from_descriptor(descriptor: Dict[str, Any]) -> Union[str, Tuple[str, str]]:
    """从角色描述符创建简洁描述，使用更紧凑的格式"""
    if not descriptor:
        return ""

    # 检查是否已经有简单的description字段
    if "description" in descriptor and isinstance(descriptor["description"], str):
        return descriptor["description"]

    # 向后兼容：如果是旧格式，继续处理复杂结构
    core_parts = []
    clothing_part = None

    # 添加基本信息
    skip_words = ['unknown', 'not specified', 'not described', 'unspecified', 'unremarkable']

    def should_skip(value):
        if not value:
            return True
        value_lower = str(value).lower()
        # 完全匹配检查
        if value_lower in [s.lower() for s in skip_words]:
            return True
        # 部分匹配检查
        for word in skip_words:
            if word in value_lower:
                return True
        return False

    # 收集核心外观特征（不包括服装）
    if 'ethnicity' in descriptor and not should_skip(descriptor['ethnicity']):
        ethnicity = str(descriptor['ethnicity']).replace('(', '').replace(')', '')
        core_parts.append(ethnicity)

    # 添加年龄阶段信息
    if 'age_stage' in descriptor and not should_skip(descriptor['age_stage']):
        age_stage = str(descriptor['age_stage']).replace('(', '').replace(')', '')
        core_parts.append(age_stage)

    if 'body_type' in descriptor and not should_skip(descriptor['body_type']):
        body_type = str(descriptor['body_type']).replace('(', '').replace(')', '')
        if 'build' not in body_type.lower():
            body_type += " build"
        core_parts.append(body_type)

    if 'hair' in descriptor and not should_skip(descriptor['hair']):
        hair = str(descriptor['hair']).replace('(', '').replace(')', '')
        core_parts.append(f"hair {hair}")

    if 'eyes' in descriptor and not should_skip(descriptor['eyes']):
        eyes = str(descriptor['eyes']).replace('(', '').replace(')', '')
        core_parts.append(f"eyes {eyes}")

    if 'distinctive_features' in descriptor and not should_skip(descriptor['distinctive_features']):
        features = str(descriptor['distinctive_features']).replace('(', '').replace(')', '')
        core_parts.append(features)

    # 单独处理服装信息，不包含在核心外观特征中
    if 'typical_clothing' in descriptor and not should_skip(descriptor['typical_clothing']):
        clothing = str(descriptor['typical_clothing']).replace('(', '').replace(')', '')
        clothing_part = f"wearing {clothing}"

    # 组合核心外观特征
    core_description = ", ".join(core_parts)

    # 如果有服装信息，添加到最终描述中但与核心特征分开
    if clothing_part:
        return core_description, clothing_part

    return core_description

# ============ Refactored Helper Functions ============

def _load_global_context(full_text: str, llm_type: Optional[str], model_key: Optional[str], verbose: bool = False) -> GlobalContext:
    """Loads global style, characters, and builds the character map."""
    ctx = GlobalContext()
    try:
        logger.info("Extracting global style and character list...")
        global_data = call_llm_json_response(
            api_function="extract_global_context", # Use the new combined API function
            prompt_data={"text": full_text},
            llm_type=llm_type, model_key=model_key, using_cache=False,
            expected_fields=["visual_style", "style_details", "all_characters"] # Expect all fields from one call
        )

        # Unpack results from the single global_data dictionary
        ctx.visual_style = global_data.get("visual_style", "")
        ctx.style_details = global_data.get("style_details", {})
        ctx.all_characters = global_data.get("all_characters", [])
        ctx.time_period = ctx.style_details.get("environment", {}).get("time_period", "")

        # Prepare simplified characters (remains the same, uses ctx.all_characters)
        for char in ctx.all_characters:
            char_name = char.get("character_name", char.get("name", ""))
            if char_name:
                # Consider if simplified_characters should also include character_id
                # For now, keeping as is, as it's mainly for regenerate_concrete_prompt LLM call.
                ctx.simplified_characters.append({"character_name": char_name})

        if verbose:
            logger.debug(f"Visual Style: {ctx.visual_style}")
            logger.debug(f"Found {len(ctx.all_characters)} characters. There are: {ctx.all_characters}")
            if not ctx.all_characters:
                logger.warning("No characters extracted from global context.")

    except Exception as global_ctx_err:
        logger.error(f"Failed to extract global context: {global_ctx_err}", exc_info=True)
        # Consider how to handle this - returning an empty context or raising?
        # For now, return the partially filled or empty context.
    return ctx

def _iter_segments(segment_texts: List[str], only_process_these_paragraphs: Union[bool, List[int]]) -> Iterator[Tuple[int, str]]:
    """Generates (segment_id, segment_text) tuples for segments to be processed."""
    indices_to_process = set()
    num_segments = len(segment_texts)

    if isinstance(only_process_these_paragraphs, list):
        valid_indices = {idx for idx in only_process_these_paragraphs if 1 <= idx <= num_segments}
        if valid_indices:
            indices_to_process = valid_indices
            logger.info(f"Processing only specified segment indices (1-based): {sorted(list(indices_to_process))}")
        else:
            logger.warning(f"Parameter 'only_process_these_paragraphs' was an empty list or contained no valid indices for {num_segments} segments. No segments will be processed.")
            return # Stop iteration
    elif only_process_these_paragraphs is True:
        logger.warning("'only_process_these_paragraphs=True' is ambiguous. Processing all segments.")
        indices_to_process = set(range(1, num_segments + 1))
    else: # False or other non-list value means process all
        indices_to_process = set(range(1, num_segments + 1))
        if num_segments > 0: # Only log if there are segments
             logger.info(f"Processing all {num_segments} segments.")

    for i, segment_text in enumerate(segment_texts):
        segment_idx = i + 1 # 1-based index
        if segment_idx in indices_to_process:
            yield segment_idx, segment_text


def _preprocess_segment(seg_id: int, text: str, llm_type: Optional[str], model_key: Optional[str]) -> SegmentCtx:
    """Performs gore replacement (conditionally based on Azure filter), hybrid scene graph extraction, and scene type classification."""
    logger.info(f"Preprocessing segment {seg_id}...")
    seg_ctx = SegmentCtx(seg_id=seg_id, raw_text=text)

    try:
        # 0. Pre-check: Use Azure built-in content filter before potential rewrite
        # Only perform this check if the LLM provider is Azure
        should_neutralize = False
        if check_azure_content_filter(text):
            logger.info(f"Segment {seg_id}: Azure content filter flagged content, proceeding with neutralization.")
            should_neutralize = True
        else:
            logger.debug(f"Segment {seg_id}: Azure content filter check passed, skipping neutralization.")

        if should_neutralize:
            # 1. Gore Neutralization (only if Azure filter flagged)
            sanitized_text, gore_flag = apply_gore_neutralization(text)
            seg_ctx.sanitized_text = sanitized_text
            seg_ctx.gore_flag = gore_flag # Flag comes from apply_gore_neutralization
            # if seg_ctx.gore_flag:
            #      logger.debug(f"Segment {seg_id}: Gore content detected/replaced by apply_gore_neutralization after Azure flag.")
            # else: apply_gore_neutralization didn't flag it despite Azure pre-check
        else:
            # Keep original text if Azure check passed or was skipped
            seg_ctx.sanitized_text = text
            seg_ctx.gore_flag = False

        # 2. Extract scene graph using HYBRID approach (using potentially sanitized text)
        seg_ctx.scene_graph = hybrid_scene_graph(seg_ctx.sanitized_text)
        logger.debug(f"Segment {seg_id}: Hybrid scene graph: {seg_ctx.scene_graph}")

        # 3. Handle Places (using potentially sanitized text's graph)
        places = seg_ctx.scene_graph.get("places")
        if not places or places == ["unspecified_location"]:
             logger.warning(f"Segment {seg_id}: Hybrid graph resulted in unspecified or empty places.")
             if places is None:
                seg_ctx.scene_graph["places"] = []

        # 4. Classify Scene Type (Initial)
        initial_scene_type = classify_scene_type(seg_ctx.scene_graph, seg_ctx.gore_flag)

        seg_ctx.initial_scene_type = initial_scene_type
        logger.debug(f"Segment {seg_id}: Initial scene type determined: {initial_scene_type}")

    except Exception as e:
        logger.error(f"Error preprocessing segment {seg_id}: {str(e)}", exc_info=True)
        seg_ctx.error = f"Preprocessing failed: {str(e)}"

    return seg_ctx

def _llm_generate_prompts(
    seg_ctx: SegmentCtx,
    global_ctx: GlobalContext,
    num_images: int,
    llm_type: Optional[str],
    model_key: Optional[str],
    with_style: bool = True
) -> List[Dict[str, Any]]:
    """Calls the LLM to generate raw image prompts for a segment."""
    logger.info(f"Segment {seg_ctx.seg_id}: Calling LLM to generate image prompts for '{seg_ctx.sanitized_text}' ...")

    prompt_suffix = "" if num_images == 1 else "s"
    costume_time_period = global_ctx.style_details.get('environment', {}).get('time_period', 'the specified')
    costume_style_desc = f"Clothing appropriate for the {costume_time_period} era"
    if global_ctx.visual_style and with_style:
         costume_style_desc += f", with distinctive characteristics of the {global_ctx.visual_style} style"

    # Directly use the scene_graph dictionary
    if not isinstance(seg_ctx.scene_graph, dict):
        logger.warning(f"Segment {seg_ctx.seg_id}: scene_graph is not a dict (Type: {type(seg_ctx.scene_graph)}). Using empty dict for prompt input.")
        scene_graph_data = {}
    else:
        scene_graph_data = seg_ctx.scene_graph

    # Extract verbs and places for the prompt template
    allowed_verbs_list = scene_graph_data.get('verbs', [])
    allowed_places_list = scene_graph_data.get('places', [])

    prompt_input = {
        "text": seg_ctx.sanitized_text,
        "visual_style": global_ctx.visual_style if with_style else "",
        "style_details": global_ctx.style_details if with_style else {},
        "all_characters": [
            {
                "character_id": char.get("character_id"),
                "character_name": char.get("character_name"),
                "aliases": char.get("aliases", [])
            }
            for char in global_ctx.all_characters
        ],
        "num_images": num_images,
        "prompt_suffix": prompt_suffix,
        "costume_style": costume_style_desc,
        "location_feel": global_ctx.style_details.get("environment", {}).get("location_feel", ""),
        "scene_graph": scene_graph_data, # Keep the full graph for reference if needed by other logic/prompts
        "forced_scene_type": seg_ctx.initial_scene_type,
        # Add the required keys for the template
        "allowed_verbs": allowed_verbs_list,
        "allowed_places": allowed_places_list
    }

    try:
        llm_output = call_llm_json_response(
            api_function="generate_storytelling_image_prompt",
            prompt_data=prompt_input,
            llm_type=llm_type, model_key=model_key, using_cache=False,
            expected_fields=["prompts"]
        )
        logger.debug(f"Generated {llm_output.get('prompts', [])} \n for input {prompt_input}.")

        if not llm_output or not isinstance(llm_output.get('prompts'), list):
            logger.error(f"Segment {seg_ctx.seg_id}: Invalid or missing 'prompts' list in LLM output. Output: {llm_output}")
            seg_ctx.error = "Invalid LLM output structure" # Add error to context
            return []
        return llm_output.get('prompts', [])

    except Exception as e:
        logger.error(f"Segment {seg_ctx.seg_id}: LLM call failed: {str(e)}", exc_info=True)
        seg_ctx.error = f"LLM call failed: {str(e)}" # Add error to context
        return []


def _enhance_characters(prompt_item: Dict[str, Any], global_ctx: GlobalContext) -> Dict[str, Any]:
    """Wrapper for character enhancement logic."""
    temp_image_prompt = {"prompts": [prompt_item]}
    enhanced_item_wrapped = enhance_prompts_with_character_info(
        temp_image_prompt, global_ctx.all_characters
    )
    if enhanced_item_wrapped and enhanced_item_wrapped.get('prompts'):
        return enhanced_item_wrapped['prompts'][0]
    else:
        logger.warning(f"Character enhancement failed for prompt item.")
        return prompt_item # Return original if enhancement fails


def _grounding_check_fix(prompt_item: Dict[str, Any], sanitized_text: str, seg_ctx: SegmentCtx, prompt_rank: int) -> Tuple[Dict[str, Any], float, bool]: # Return score and pass/fail status
    """Performs grounding check using CrossEncoder and potentially fixes scene type."""
    min_score_threshold = getattr(settings, 'GROUNDING_MIN_SCORE', 0.70) # Use new cross-encoder min score
    score = 0.0 # Initialize score
    passed = True # Assume pass initially

    # Determine scene type *after* potential LLM override & manual correction
    final_scene_type = prompt_item.get('scene_type')

    # --- Grounding Check Condition: Check if final type is concrete --- #
    if final_scene_type == 'concrete':
         blocks = prompt_item.get('blocks', {})
         subject = blocks.get('subject_action', '')
         setting = blocks.get('setting', '')
         # === Recommendation 3: Enhance Grounding Scope ===
         clothing = blocks.get('clothing', '') # Add clothing
         env_elements = blocks.get('environment_elements', []) # Add env elements
         env_elements_str = ", ".join(env_elements) if isinstance(env_elements, list) else ''
         # Combine more elements for a richer representation
         representative_text = f"{subject} {setting} {clothing} {env_elements_str}".strip()
         # === End Recommendation 3 ===
         if representative_text:
             # Use the new cross_encoder_score function
             score = cross_encoder_score(representative_text, sanitized_text)

             # Calculate dynamic threshold (optional, based on blueprint)
             # Note: Scoring self-similarity might be slow and 'base' isn't strictly 1.0
             # base_score = cross_encoder_score(sanitized_text, sanitized_text) # Optional: slow
             # dynamic_threshold = max(min_score_threshold, base_score * 0.8) # Optional
             threshold = min_score_threshold # Use fixed threshold for now

             logger.debug(f"Segment {seg_ctx.seg_id}, Prompt {prompt_rank}: Grounding Check Score: {score:.4f} (Threshold: {threshold:.2f})")
             if score < threshold:
                 passed = False
                 logger.warning(f"Segment {seg_ctx.seg_id}, Prompt {prompt_rank}: Grounding Check failed (Score {score:.4f} < {threshold:.2f}) for concrete scene. Forcing type to 'abstract'.")
                 prompt_item['scene_type'] = "abstract" # Overwrite scene type
             # else: # Grounding passed
                 # --- Freeze Seed Here ---
                 # Moved seed calculation here as per blueprint
                 # if 'seed' not in prompt_item:
                 #    # Generate seed based on seg_id, rank, and time for uniqueness
                 #    seed_val = hashlib.md5(f"{seg_ctx.seg_id}_{prompt_rank}_{time.time()}".encode()).hexdigest()[:8]
                 #    prompt_item['seed'] = int(seed_val, 16) % (2**32) # Ensure valid int seed
                 #    logger.debug(f"Segment {seg_ctx.seg_id}, Prompt {prompt_rank}: Grounding passed, Seed generated: {prompt_item['seed']}")
                 # ^^ Moved seed calculation outside this function to avoid complex return values

    else: # Abstract scene
         logger.debug(f"Segment {seg_ctx.seg_id}, Prompt {prompt_rank}: Skipping Grounding Check (scene type is not concrete).")
         prompt_item['scene_type'] = 'abstract' # Ensure scene type is abstract if not concrete
         passed = True # Abstract scenes pass grounding by default

    return prompt_item, score, passed # Return updated item, score, and pass/fail status

# --- Helper to extract used verbs/places from prompt blocks ---
def _extract_used_elements(blocks: dict, graph: dict) -> Tuple[set, set]:
    used_verbs = set()
    used_places = set()
    subject_action = blocks.get('subject_action', '').lower()
    setting = blocks.get('setting', '').lower()

    # Simple word matching (can be improved with tokenization/lemmatization if needed)
    for verb in graph.get('verbs', []):
        if verb.lower() in subject_action:
            used_verbs.add(verb)
    for place in graph.get('places', []):
         # Avoid matching "unspecified_location" as a used place
        if place != "unspecified_location" and place.lower() in setting:
            used_places.add(place)
    return used_verbs, used_places


def _postprocess_prompts(
    seg_ctx: SegmentCtx,
    raw_prompts: List[Dict[str, Any]],
    global_ctx: GlobalContext,
    with_style: bool = True,
    llm_type: Optional[str] = None, # Added for retry
    model_key: Optional[str] = None  # Added for retry
) -> List[Dict[str, Any]]:
    """Processes raw LLM prompts: enhances characters, builds flux prompts, runs grounding checks, handles retries, calculates seeds."""
    processed_prompts = []
    detected_characters_segment = set() # Use set for uniqueness based on ID/name

    for prompt_idx, prompt_item in enumerate(raw_prompts):
        if not isinstance(prompt_item, dict):
            logger.warning(f"Segment {seg_ctx.seg_id}: Skipping non-dictionary item in prompts list at index {prompt_idx}.")
            continue

        current_rank = prompt_item.get('chronological_rank', prompt_idx + 1)
        #logger.debug(f"Segment {seg_ctx.seg_id}, Prompt {current_rank}: Processing item...")

        prompt_item.setdefault('blocks', {})
        prompt_item.setdefault('characters', [])
        prompt_item.setdefault('negative_prompt', '') # Ensure negative prompt exists

        # 1. Character Enhancement (Now runs on the LLM-provided character_ids)
        # Important: Run this BEFORE grounding check if seed depends on characters
        prompt_item = _enhance_characters(prompt_item, global_ctx)

        # Add detected character IDs to segment set
        # (Uses character_id from prompt_item['characters'], which is now directly from LLM)
        for char_obj in prompt_item.get('characters', []): # char_obj is like {'character_id': ..., 'descriptor': ...}
             char_id = char_obj.get("character_id")
             if char_id:
                 detected_characters_segment.add(char_id) # Store unique character_ids

        # 2. Correct/Enforce Scene Type based on initial check + LLM output
        scene_type_from_llm = prompt_item.get('scene_type', seg_ctx.initial_scene_type)
        prompt_item['scene_type'] = seg_ctx.initial_scene_type # Always enforce the pre-processed type

        # 3. Grounding Check & Seed Generation (Simplified)
        grounding_passed = True # Assume pass initially for abstract or if concrete passes
        grounding_score = 0.0
        mismatch_report = None # Initialize mismatch report

        if prompt_item['scene_type'] == 'concrete':
            # Perform the single grounding check
            blocks = prompt_item.get('blocks', {})
            subject = blocks.get('subject_action', '')
            setting = blocks.get('setting', '')
            # === Recommendation 3: Enhance Grounding Scope ===
            clothing = blocks.get('clothing', '') # Add clothing
            env_elements = blocks.get('environment_elements', []) # Add env elements
            env_elements_str = ", ".join(env_elements) if isinstance(env_elements, list) else ''
            # Combine more elements for a richer representation
            representative_text = f"{subject} {setting} {clothing} {env_elements_str}".strip()
            # === End Recommendation 3 ===
            if representative_text:
                score = cross_encoder_score(representative_text, seg_ctx.sanitized_text)
                grounding_score = score # Store score
                threshold = getattr(settings, 'GROUNDING_MIN_SCORE', 0.70)
                logger.debug(f"Segment {seg_ctx.seg_id}, Prompt {current_rank}: Grounding Check Score: {score:.4f} (Threshold: {threshold:.2f})")
                if score < threshold:
                    grounding_passed = False
                    logger.warning(f"Segment {seg_ctx.seg_id}, Prompt {current_rank}: Grounding Check FAILED (Score {score:.4f} < {threshold:.2f}). Attempting LLM retry.")
                    prompt_item['scene_type'] = "abstract" # Tentatively downgrade, will revert on successful retry

                    # Calculate mismatch report *before* logging and retry
                    used_verbs, used_places = _extract_used_elements(blocks, seg_ctx.scene_graph)
                    graph_verbs = set(seg_ctx.scene_graph.get('verbs', []))
                    graph_places = set(p for p in seg_ctx.scene_graph.get('places', []) if p != "unspecified_location") # Exclude placeholder
                    mismatch_report = {
                        "missing_verbs": list(graph_verbs - used_verbs),
                        "missing_places": list(graph_places - used_places),
                        "similarity_score": round(grounding_score, 4)
                    }
                    log_grounding_failure(seg_ctx.seg_id, current_rank, grounding_score, seg_ctx.sanitized_text, representative_text, mismatch_report)

                    # === Recommendation 2: LLM Retry Logic ===
                    try:
                        logger.info(f"Segment {seg_ctx.seg_id}, Prompt {current_rank}: Calling LLM to regenerate concrete prompt blocks.")
                        retry_prompt_input = {
                            "original_text": seg_ctx.sanitized_text,
                            "failed_blocks": blocks, # Pass the failed blocks
                            "scene_graph": seg_ctx.scene_graph,
                            "scene_graph_json": json.dumps(seg_ctx.scene_graph), # Pass JSON string if template needs it
                            "visual_style": global_ctx.visual_style if with_style else "",
                            "style_details": global_ctx.style_details if with_style else {},
                            "all_characters": global_ctx.simplified_characters,
                            "costume_style": global_ctx.style_details.get('environment', {}).get('time_period', 'the specified'), # Get costume era
                            "location_feel": global_ctx.style_details.get("environment", {}).get("location_feel", ""),
                            "mismatch_report": mismatch_report
                        }
                        # Expecting only the 'blocks' dictionary as output
                        retry_output = call_llm_json_response(
                            api_function="regenerate_concrete_prompt",
                            prompt_data=retry_prompt_input,
                            llm_type=llm_type, model_key=model_key, using_cache=False,
                            expected_fields=None # Expecting a raw dict (the blocks)
                        )

                        # --- Updated Validation Logic ---
                        accepted_blocks = None
                        if isinstance(retry_output, dict) and retry_output:
                            required_keys = {'subject_action', 'setting', 'clothing', 'style', 'camera', 'mood'}
                            # Check if the dictionary itself contains the keys
                            if required_keys.issubset(retry_output.keys()):
                                accepted_blocks = retry_output
                            # OR if it has a 'blocks' key containing them (handle potential nesting)
                            elif isinstance(retry_output.get('blocks'), dict) and required_keys.issubset(retry_output['blocks'].keys()):
                                 accepted_blocks = retry_output['blocks']
                            # OR if it has a 'blocks' array with dictionaries containing the keys
                            elif isinstance(retry_output.get('blocks'), list) and retry_output['blocks'] and isinstance(retry_output['blocks'][0], dict):
                                # Check if the first item in the blocks array has all required keys
                                if required_keys.issubset(retry_output['blocks'][0].keys()):
                                    accepted_blocks = retry_output['blocks'][0]
                                    logger.debug(f"Segment {seg_ctx.seg_id}, Prompt {current_rank}: Found valid blocks in the first item of blocks array.")

                        if accepted_blocks: # If we found a valid blocks dictionary
                            logger.info(f"Segment {seg_ctx.seg_id}, Prompt {current_rank}: LLM retry SUCCEEDED. Updating blocks and reverting scene type to concrete.")
                            prompt_item['blocks'] = accepted_blocks # Use the found blocks
                            prompt_item['scene_type'] = "concrete" # Revert downgrade
                            grounding_passed = True # Assume retry fixed it
                        else:
                            # Log the actual retry_output for better debugging
                            logger.warning(f"Segment {seg_ctx.seg_id}, Prompt {current_rank}: LLM retry failed, returned invalid structure, or missing required keys. Keeping scene type abstract. Raw Output: {retry_output}")
                            # Keep scene type abstract (already set before retry)
                        # --- End Updated Validation Logic ---

                    except Exception as retry_err:
                        logger.error(f"Segment {seg_ctx.seg_id}, Prompt {current_rank}: LLM retry call failed with exception: {retry_err}", exc_info=True)
                        # Keep scene type as abstract
                    # === End Recommendation 2 ===

            else: # Representative text was empty
                # If no representative text, consider it a failure or keep as concrete?
                # Forcing abstract might be safer if subject/setting are empty
                logger.warning(f"Segment {seg_ctx.seg_id}, Prompt {current_rank}: Concrete scene has empty representative text for grounding. Forcing abstract.")
                prompt_item['scene_type'] = "abstract"
                grounding_passed = False

        # Ensure abstract scenes are marked as abstract and have no characters
        if prompt_item['scene_type'] != 'concrete':
            # prompt_item['scene_type'] = 'abstract' # Removed: This line is redundant here
            # Ensure no characters in abstract scenes
            if prompt_item.get('characters'): # Only clear if characters list exists and is not empty
                logger.debug(f"Segment {seg_ctx.seg_id}, Prompt {current_rank}: Clearing characters list as final scene type is abstract.")
                prompt_item['characters'] = []
            # Abstract scenes always "pass" grounding conceptually, but 'grounding_passed' flag reflects if a *concrete* attempt failed.
            # We don't set grounding_passed=True here, as it's used for seed generation conditional on concrete success.

        # --- Generate Seed ONLY if Grounding PASSED (for concrete, potentially after retry) ---
        if prompt_item['scene_type'] == 'concrete' and grounding_passed and 'seed' not in prompt_item:
            seed_hex = hashlib.md5(f"{seg_ctx.seg_id}_{current_rank}_{time.time()} {prompt_item.get('blocks')}".encode()).hexdigest()[:8] # Add blocks to seed calculation?
            seed_val = int(seed_hex, 16) % (2**32)
            prompt_item['seed'] = seed_val
            logger.debug(f"Segment {seg_ctx.seg_id}, Prompt {current_rank}: Grounding PASSED (Score: {grounding_score:.4f}). Seed generated: {seed_val} (from {seed_hex})")

        final_scene_type = prompt_item.get('scene_type') # Get final type after potential downgrade/retry

        # 4. Build Flux Prompt (using the final_scene_type)
        blocks = prompt_item.get('blocks', {})
        anchor_limit = 3 if final_scene_type == "concrete" else 2
        anchors = [ch.get('descriptor', '') for ch in prompt_item.get('characters', [])[:anchor_limit] if ch.get('descriptor')]

        # 收集角色的服装信息
        character_clothing = []
        for ch in prompt_item.get('characters', [])[:anchor_limit]:
            if ch.get('clothing'):
                character_clothing.append(ch.get('clothing'))

        scene_bits = []
        if final_scene_type == "concrete":
            # 首先添加主要动作和场景
            scene_bits = [blocks.get('subject_action', ''), blocks.get('setting', '')]

            # 然后添加服装信息（优先使用角色的服装信息，如果没有则使用blocks中的clothing）
            if character_clothing:
                scene_bits.extend(character_clothing)
            elif blocks.get('clothing'):
                scene_bits.append(blocks.get('clothing'))

            # 最后添加环境元素
            if blocks.get('environment_elements'):
                scene_bits.append(blocks.get('environment_elements'))

            # 过滤掉空字符串
            scene_bits = [bit for bit in scene_bits if bit]
        elif final_scene_type == "abstract":
            env = blocks.get('symbolic_environment', '')
            if isinstance(env, list):
                scene_bits = [", ".join(filter(None, env))]
            elif isinstance(env, str) and env:
                scene_bits = [env]
            comp_cue = blocks.get('composition_cue', '')
            if comp_cue:
                scene_bits.append(comp_cue)

        style_bits = [blocks.get('style', '')] if blocks.get('style', '') else []
        camera_bits = [blocks.get('camera', '')] if blocks.get('camera', '') else []
        mood_bits = [blocks.get('mood', '')] if blocks.get('mood', '') else []

        try:
            current_flux_prompt = build_flux_prompt(
                anchors, scene_bits, style_bits, camera_bits, mood_bits,
                final_scene_type, with_style, time_period=global_ctx.time_period
            )
            prompt_item['flux_prompt'] = current_flux_prompt
            logger.debug(f"Segment {seg_ctx.seg_id}, Prompt {current_rank}: Built flux prompt: {current_flux_prompt}...")
        except Exception as e:
            logger.error(f"Segment {seg_ctx.seg_id}, Prompt {current_rank}: Failed to build flux prompt: {e}", exc_info=True)
            current_flux_prompt = f"{final_scene_type} scene build failed" # Fallback
            prompt_item['flux_prompt'] = current_flux_prompt

        processed_prompts.append(prompt_item)

    return processed_prompts


def _ctx_to_dict(seg_ctx: SegmentCtx, global_ctx: GlobalContext) -> Dict[str, Any]:
    """Converts a SegmentCtx object back to the legacy dictionary format."""
    # Extract detected characters for this segment
    detected_characters_segment = set() # This set will contain character_ids
    for prompt_item in seg_ctx.processed_prompts:
        for char_in_prompt in prompt_item.get('characters', []): # char_in_prompt is {'character_id': ..., 'descriptor': ...}
             char_id = char_in_prompt.get("character_id")
             if char_id:
                 detected_characters_segment.add(char_id)

    unique_chars_list = []
    # char_keys_added should be char_ids_added
    char_ids_added = set()
    # detected_characters_segment now contains character_ids
    for char_id_val in detected_characters_segment: # char_id_val is a character_id string
        if char_id_val and char_id_val not in char_ids_added:
            # Find the character in global_ctx.all_characters by this ID
            char_info = next((c for c in global_ctx.all_characters if c.get('character_id') == char_id_val), None)
            if char_info:
                unique_chars_list.append(char_info)
                char_ids_added.add(char_id_val)


    return {
        "segment_id": seg_ctx.seg_id,
        "segment_text": seg_ctx.raw_text, # Original text
        "error": seg_ctx.error,
        "image_prompt": {"prompts": seg_ctx.processed_prompts}, # Contains the list of prompt dicts
        # --- Add other useful info from context ---
        "sanitized_text": seg_ctx.sanitized_text,
        "scene_graph": seg_ctx.scene_graph,
        "initial_scene_type": seg_ctx.initial_scene_type,
        "style_details": global_ctx.style_details, # Add global style details for reference
        "detected_characters": unique_chars_list, # Add detected characters
    }

# ============ Top-level orchestrator =========
def generate_story_image_prompts(
    full_text: str,
    segment_texts: List[str],
    num_images_list: List[int] = None,
    llm_type: str = None,
    model_key: str = None,
    verbose: bool = False, # Keep verbose for logging control within helpers
    only_process_these_paragraphs: Union[bool, List[int]] = False,
    with_style: bool = True
) -> List[Dict[str, Any]]:
    """
    Orchestrates the generation of image prompts for story segments using helper functions.
    (Refactored for clarity and maintainability)

    Args:
        full_text: 完整的故事文本
        segment_texts: 段落文本列表
        num_images_list: 每个段落需要的图片数量列表，默认每个段落1张
        llm_type: LLM类型
        model_key: 模型键名
        verbose: 是否显示详细日志
        only_process_these_paragraphs: Bool (False=process all) or List of 1-based indices to process.
        with_style: 是否在生成的提示词中包含风格部分

    Returns:
        包含每个段落的图像提示和相关信息的字典列表 (旧格式)
    """
    if num_images_list is None:
        num_images_list = [1] * len(segment_texts)
    if len(num_images_list) < len(segment_texts):
        num_images_list.extend([1] * (len(segment_texts) - len(num_images_list)))

    if not segment_texts:
        logger.warning("No segment texts provided, cannot generate prompts.")
        return []

    # --- Main Processing Logic ---
    results_ctx: List[SegmentCtx] = []
    results_map: Dict[int, SegmentCtx] = {} # For quick lookup by seg_id

    # 1. Load Global Context (only if segments will be processed)
    # Determine if any segments will be processed before loading context
    # (Optimization: avoid LLM calls if only_process.. results in empty set)
    should_process_any = False
    if isinstance(only_process_these_paragraphs, list):
        valid_indices = {idx for idx in only_process_these_paragraphs if 1 <= idx <= len(segment_texts)}
        if valid_indices: should_process_any = True
    elif only_process_these_paragraphs is True:
        if len(segment_texts) > 0: should_process_any = True
    else: # False means process all
        if len(segment_texts) > 0: should_process_any = True

    global_ctx = GlobalContext() # Initialize empty context
    if should_process_any:
        global_ctx = _load_global_context(full_text, llm_type, model_key, verbose)
        # If global context fails critically, maybe stop early?
        # Currently _load_global_context logs errors but returns potentially empty context.

    # 2. Iterate and Process Segments
    for seg_id, seg_text in _iter_segments(segment_texts, only_process_these_paragraphs):
        # Get number of images for this specific segment
        current_num_images = num_images_list[seg_id - 1] # Adjust index

        # Preprocess (SRL, Scene Type, etc.)
        seg_ctx = _preprocess_segment(seg_id, seg_text, llm_type, model_key)
        if seg_ctx.error:
            results_ctx.append(seg_ctx)
            results_map[seg_id] = seg_ctx
            continue

        # Generate Raw Prompts via LLM
        raw_llm_prompts = _llm_generate_prompts(seg_ctx, global_ctx, current_num_images, llm_type, model_key, with_style)
        if seg_ctx.error: # Check if LLM call added an error
            results_ctx.append(seg_ctx)
            results_map[seg_id] = seg_ctx
            continue
        if not raw_llm_prompts: # Handle case where LLM returns empty list without error flag
            logger.warning(f"Segment {seg_id}: LLM returned no prompts.")
            # Decide how to handle: add error or just empty prompts?
            # seg_ctx.error = "LLM returned no prompts"
            results_ctx.append(seg_ctx)
            results_map[seg_id] = seg_ctx
            continue

        # Postprocess Prompts (Enhance, Flux, Grounding, Seed)
        seg_ctx.processed_prompts = _postprocess_prompts(seg_ctx, raw_llm_prompts, global_ctx, with_style, llm_type, model_key)

        results_ctx.append(seg_ctx)
        results_map[seg_id] = seg_ctx # Add to map for FPA application

    # 5. Convert results back to legacy dictionary format
    final_results = [_ctx_to_dict(r, global_ctx) for r in results_ctx]

    return final_results


# ============ Existing Wrapper & Helper Functions (generate_prompts_for_paragraphs, build_flux_prompt, compute_seed_from_characters) ============

# Keep generate_prompts_for_paragraphs as a wrapper
def generate_prompts_for_paragraphs(paragraphs: List[Dict], theme: str, full_text: str = None, only_process_these_paragraphs: Union[bool, List[int]] = False, with_style: bool = True, llm_type: str = None, model_key: str = None) -> List[Dict]: # Added llm params & updated type hint
    """为段落列表生成图像提示词 (Wrapper function)"""
    if not paragraphs:
        logger.warning("No paragraphs provided to generate_prompts_for_paragraphs.")
        return []

    if full_text is None:
        # Ensure paragraph_text exists and is a string
        paragraph_texts = [p.get('paragraph_text', '') for p in paragraphs if isinstance(p.get('paragraph_text'), str)]
        full_text = "\n".join(paragraph_texts)
        if not full_text:
             logger.warning("Could not construct full_text from paragraphs.")
             # Decide how to proceed: return error, or try without full_text?
             # For now, let generate_story_image_prompts handle empty full_text if needed
             pass

    segment_texts = [p.get('paragraph_text', '') for p in paragraphs if isinstance(p.get('paragraph_text'), str)]
    # Ensure num_images is an int, default to 1
    num_images_list = [int(p.get('num_images_needed', 1)) if str(p.get('num_images_needed', 1)).isdigit() else 1 for p in paragraphs]

    if not segment_texts:
         logger.warning("Paragraph list provided but no actual text found to process.")
         # Return original structure, maybe add errors
         output_paragraphs = []
         for idx, p in enumerate(paragraphs):
              current_paragraph = p.copy() if isinstance(p, dict) else {'original_index': idx, 'original_data': p}
              current_paragraph['error'] = current_paragraph.get('error', 'No text found in paragraph')
              current_paragraph['prompt'] = {}
              output_paragraphs.append(current_paragraph)
         return output_paragraphs

    try:
        logger.info(f"Calling generate_story_image_prompts for {len(segment_texts)} paragraphs... Theme: {theme}")
        # Call the main generation function with the new logic
        # Pass llm_type and model_key from wrapper
        chain_results = generate_story_image_prompts(
            full_text=full_text,
            segment_texts=segment_texts,
            num_images_list=num_images_list,
            verbose=True, # Enable verbose logging from the core function
            only_process_these_paragraphs=only_process_these_paragraphs, # Pass updated param
            with_style=with_style,
            llm_type=llm_type, # Pass through LLM config
            model_key=model_key
        )

        # Map results back to the original paragraph structure using segment_id
        results_map = {res.get("segment_id"): res for res in chain_results if isinstance(res, dict) and res.get("segment_id") is not None}

        output_paragraphs = []
        for idx, paragraph_data in enumerate(paragraphs):
            # Ensure the original paragraph data is a dictionary for easy update
            # If not, create a basic dict structure to hold results/errors
            if isinstance(paragraph_data, dict):
                 current_paragraph = paragraph_data.copy() # Work on a copy
            else:
                 current_paragraph = {'original_index': idx, 'original_data': paragraph_data, 'prompt': {}, 'error': None}
                 logger.warning(f"Input item at index {idx} was not a dict. Wrapping data.")

            # Use segment_id from the result if available, otherwise from original or index
            segment_id_from_result = None
            temp_lookup_key = idx + 1 # Use 1-based index for initial lookup
            if temp_lookup_key in results_map:
                segment_id_from_result = results_map[temp_lookup_key].get("segment_id")

            # Determine the segment_id to add to the output
            # Priority: Result -> Original Paragraph -> Index+1
            output_segment_id = segment_id_from_result or current_paragraph.get("segment_id") or (idx + 1)
            # Add the determined segment_id to the current_paragraph dict being built
            current_paragraph["segment_id"] = output_segment_id

            # Now use the correctly determined output_segment_id for map lookup
            if output_segment_id in results_map:
                result = results_map[output_segment_id]
                # Merge result data into the current paragraph dict
                current_paragraph.update(result) # This overwrites keys like 'prompt', 'error' etc.
                # Clean up keys that might be redundant from the result dict if needed
                current_paragraph.pop('segment_text', None) # Keep original paragraph_text
                # Ensure prompt key exists
                if 'image_prompt' in current_paragraph:
                     current_paragraph['prompt'] = current_paragraph.pop('image_prompt')
                else:
                     current_paragraph['prompt'] = {} # Ensure prompt key exists even if empty

                if result.get("error"):
                    error_msg = f"Processing failed: {result['error']}"
                    logger.error(f"Paragraph {output_segment_id} (Original Index {idx}) failed: {error_msg}")
                    current_paragraph['error'] = error_msg # Ensure error is set
                else:
                    current_paragraph.pop('error', None) # Remove error key if successful

            else:
                # Handle case where a segment was filtered out or processing failed early
                missing_msg = "Processing result missing or segment skipped"
                logger.warning(f"Paragraph {output_segment_id} (Original Index {idx}) has no corresponding result.")
                current_paragraph['prompt'] = current_paragraph.get('prompt', {}) # Keep existing prompt if any
                current_paragraph['error'] = current_paragraph.get('error', missing_msg) # Set or update error

            output_paragraphs.append(current_paragraph)

        return output_paragraphs

    except Exception as e:
        logger.error(f"Error in generate_prompts_for_paragraphs wrapper: {str(e)}", exc_info=True)
        # Add error to all paragraphs if the whole process fails
        output_paragraphs = []
        for idx, p_data in enumerate(paragraphs):
             if isinstance(p_data, dict):
                  p_copy = p_data.copy()
             else:
                  p_copy = {'original_index': idx, 'original_data': p_data}
             p_copy['prompt'] = {}
             p_copy['error'] = f"Global failure: {str(e)}"
             output_paragraphs.append(p_copy)
        return output_paragraphs

def build_flux_prompt(
    anchors: List[str],
    scene_bits: List[str],
    style_bits: List[str],
    camera_bits: List[str],
    mood_bits: List[str],
    scene_type: str = "concrete",
    with_style: bool = True,
    time_period: str = ""
) -> str:
    try:
        # 直接使用分号连接角色描述，不需要额外处理
        # 新格式的角色描述已经是 "Name (description)" 格式
        anchor_part = "; ".join(filter(None, anchors))

        # 处理场景描述
        all_scene_bits = []
        for bit in scene_bits:
            if not bit:
                continue
            if isinstance(bit, list):
                all_scene_bits.extend([str(item).strip() for item in bit if item]) # Ensure strings and strip
            else:
                all_scene_bits.append(str(bit).strip()) # Ensure string and strip

        # 将所有场景描述连接成一个字符串，按优先级和层次排序
        combined_scene = ", ".join(filter(None, all_scene_bits)) # Filter empty strings after potential stripping

        # 不再需要从场景描述中移除角色名称，因为角色信息现在与场景动作明确分开

        # 处理其他部分
        style_part = ", ".join([str(bit).strip() for bit in style_bits if bit]) if with_style else ""
        camera_part = ", ".join([str(bit).strip() for bit in camera_bits if bit])
        mood_part = ", ".join([str(bit).strip() for bit in mood_bits if bit])

        # 组装顺序：anchors — scene, camera, mood, style, time_period
        # 使用 em-dash (—) 明确分隔人物元数据和场景动作
        parts = []

        # 只有当角色描述和场景描述都存在时，才使用 em-dash 分隔
        if anchor_part and combined_scene:
            parts.append(f"{anchor_part} — {combined_scene}")
        elif anchor_part: # 只有角色描述
            parts.append(anchor_part)
        elif combined_scene: # 只有场景描述
            parts.append(combined_scene)

        # 按优先级和层次添加其他部分
        # 从动作→地点→服装→道具→风格→视角→情绪→时代
        # 注意：当前实现中，这些元素已经在各自的部分中组织好了
        if camera_part: parts.append(camera_part)
        if mood_part: parts.append(mood_part)
        if style_part: parts.append(style_part)
        if time_period: parts.append(time_period)

        # 用逗号连接所有部分
        result = ", ".join(filter(None, parts))
        logger.debug(f"Flux prompt (type: {scene_type}): '{result}'")
        return result
    except Exception as e:
        logger.error(f"构建flux_prompt时出错: {str(e)}")
        logger.debug("错误详情:", exc_info=True)
        # Fallback to just scene type or empty string?
        return scene_type if scene_type else ""

def compute_seed_from_characters(characters: List[Dict[str, Any]]) -> Optional[int]:
    """
    (Legacy Seed Calculation) Generate a stable 32-bit seed from character_id list.
    New logic uses seg_id, rank, and time after grounding check.
    """
    ids = sorted([str(c.get("character_id", "")) for c in characters if c.get("character_id")]) # Ensure IDs are strings
    if not ids:
        return None
    digest = hashlib.sha256("_".join(ids).encode("utf-8")).hexdigest()
    return int(digest, 16) % (2**32)

