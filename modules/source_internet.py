import logging
from typing import List, Dict, Any, Tuple
from modules.langchain_interface import call_llm_json_response
from modules.tavily_search import search_tavily, process_results
from modules.utils import normalize_language, calculate_token_count
import os
import json
from modules.image_embedding import generate_embeddings
from fetch_process_images import download_images, get_image_metadata_path
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import logger, IMAGE_DIR

MAX_CONTEXT_TOKENS = 100000
logger = logging.getLogger(__name__)

# 在文件顶部添加 DEBUG_MODE 变量
DOWNLOAD_IMAGES = False
CLIP_THRESHOLD = 0.25
ENABLE_IMAGE_RESULTS = False  # 新增控制变量

def extract_keyword(script_content: str, language: str) -> str:
    """
    从脚本内容中提取搜索关键词
    
    Args:
        script_content (str): 脚本内容
        language (str): 目标语言
        
    Returns:
        str: 搜索关键词
    """
    try:
        response = call_llm_json_response(
            api_function="gen_keyword",
            prompt_data={
                "script": script_content,
                "LANGUAGE": language
            },
            using_cache=False
        )
        
        if not response or "keyword" not in response:
            logger.error("提取关键词失败：无效的响应格式")
            return ""
            
        return response["keyword"]
            
    except Exception as e:
        logger.error(f"提取关键词时发生错误: {str(e)}")
        return ""

def get_external_content(search_query: str, theme: str, output_dir: str = "output", language: str = "zh", wiki_only: bool = False) -> Tuple[List[str], List[Dict[str, Any]]]:
    """获取外部内容和相关图片
    
    Args:
        search_query (str): 搜索关键词
        theme (str): 主题名称，用于图片保存和元数据
        output_dir (str): 图片保存目录
        language (str): 目标语言，只有在英语(en)时才下载图片
        wiki_only (bool): 是否只搜索维基百科
        
    Returns:
        Tuple[List[str], List[Dict[str, Any]]]: 返回文本内容列表和图片信息列表
    """
    results, images = search_tavily(search_query, wiki_only=wiki_only)
    if not results or not isinstance(results, list):
        logger.error(f"No valid results found for query: {search_query}")
        return [], []

    # 处理文本内容
    total_tokens = 0
    raw_contents = []

    for result in results:
        if not isinstance(result, dict) or 'raw_content' not in result:
            logger.debug(f"Skipping invalid result: {result}")
            continue

        raw_content = result['raw_content']
        if raw_content is None:
            continue

        tokens = calculate_token_count(raw_content)

        if total_tokens + tokens > MAX_CONTEXT_TOKENS:
            logger.debug(f"Skipping content due to token limit. Current tokens: {total_tokens}, Attempted addition: {tokens}")
            break

        total_tokens += tokens
        raw_contents.append(raw_content)

    if not raw_contents:
        logger.error(f"No raw content could be added due to token limitations for query: {search_query}")
        return [], []

    logger.info(f"Final added external content token : {total_tokens}, Length of raw_contents: {len(''.join(raw_contents))} characters")
    
    # 标准化语言代码并检查是否为英语
    normalized_lang = normalize_language(language)
    if not DOWNLOAD_IMAGES or normalized_lang != "en":
        logger.info(f"Skip image download: DOWNLOAD_IMAGES={DOWNLOAD_IMAGES}, language={normalized_lang}")
        return raw_contents, []
        
    # 使用 fetch_process_images.py 中的功能处理图片
    image_dir = IMAGE_DIR.format(theme=theme)
    download_images(theme=theme, output_dir=image_dir, keyword=search_query)
    
    # 读取处理后的元数据
    metadata_path = get_image_metadata_path(image_dir, theme)
    try:
        with open(metadata_path, 'r', encoding='utf-8') as f:
            image_results = json.load(f)
    except Exception as e:
        logger.error(f"读取图片元数据失败: {e}")
        image_results = []
    
    return raw_contents, image_results