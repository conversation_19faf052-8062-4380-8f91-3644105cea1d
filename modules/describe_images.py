#utils/describe_images.py
from typing import List, Tuple
from dotenv import load_dotenv
import re
from modules.langchain_interface import call_llm, call_llm_json_response
from config import logger, FILTERED_IMAGE_DIR
import logging
import json
import os
import numpy as np
import shutil

logger = logging.getLogger(__name__)
# Load environment variables
load_dotenv()

def is_content_filter_error(error_msg: str) -> bool:
    """
    判断错误是否为内容过滤导致
    
    Args:
        error_msg (str): 错误信息
        
    Returns:
        bool: 如果是内容过滤错误返回True，否则返回False
    """
    if "content filter being triggered" in error_msg:
        return True
    if "content management policy" in error_msg and "content_filter" in error_msg:
        return True
    return False

def move_filtered_image_to_temp(image_url: str, base_dir: str = None, theme: str = None) -> bool:
    """
    将被内容过滤的图片移动到temp目录
    
    Args:
        image_url (str): 图片URL或路径
        base_dir (str, optional): 基础目录，如果为None则使用当前目录
        theme (str, optional): 主题名称，用于格式化FILTERED_IMAGE_DIR路径
        
    Returns:
        bool: 移动成功返回True，否则返回False
    """
    try:
        # 尝试从URL中提取文件名
        image_filename = os.path.basename(image_url)
        
        # 如果没有指定主题，尝试从base_dir中提取
        if not theme and base_dir:
            # 尝试从路径中提取主题名称
            match = re.search(r'results/([^/]+)', base_dir)
            if match:
                theme = match.group(1)
        
        # 如果仍然没有主题，使用默认名称
        if not theme:
            theme = "unknown_theme"
            logger.warning(f"未指定主题名称，使用默认值: {theme}")
        
        # 创建filtered_images目录（使用config中定义的路径）
        temp_dir = FILTERED_IMAGE_DIR.format(theme=theme)
        os.makedirs(temp_dir, exist_ok=True)
        
        # 如果没有指定基础目录，使用当前目录
        if not base_dir:
            base_dir = os.path.dirname(os.path.abspath(__file__))
            base_dir = os.path.dirname(base_dir)  # 上一级目录
        
        # 尝试从本地文件系统找到该图片
        for root, dirs, files in os.walk(base_dir):
            if "temp" in root:  # 跳过temp目录
                continue
                
            for file in files:
                if file == image_filename or image_url.endswith(file):
                    source_path = os.path.join(root, file)
                    target_path = os.path.join(temp_dir, file)
                    
                    # 移动文件
                    shutil.move(source_path, target_path)
                    logger.info(f"已将被过滤的图片 {file} 从 {source_path} 移动到 {temp_dir}")
                    return True
        
        # 如果是远程URL且无法在本地找到，记录日志
        logger.warning(f"无法在本地文件系统找到被过滤的图片: {image_url}")
        
        # 将URL保存到记录文件中
        filtered_urls_file = os.path.join(temp_dir, "filtered_urls.txt")
        with open(filtered_urls_file, "a", encoding="utf-8") as f:
            f.write(f"{image_url}\n")
        logger.info(f"已将被过滤的图片URL记录到: {filtered_urls_file}")
        
        return False
        
    except Exception as e:
        logger.error(f"移动被过滤图片时出错: {str(e)}")
        return False

def generate_keyword(translated_script_file: str, language: str) -> str:
    """
    根据翻译后的脚本文件生成关键词，并去除特殊符号
    
    Args:
    translated_script_file (str): 翻译后的脚本文件路径
    language (str): 生成关键词的语言
    
    Returns:
    str: 处理后的关键词
    """
    try:
        with open(translated_script_file, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        input_data = {
            "script": script_content,
            "LANGUAGE": language
        }
        
        result = call_llm_json_response(
            api_function="gen_keyword",
            prompt_data=input_data,
            expected_fields=["keyword"],
            using_cache=False   
        )
        
        if result and "keyword" in result:
            keyword = result["keyword"]
            # 去除特殊符号
            cleaned_keyword = re.sub(r'[^\w\s]', '', keyword)
            logger.info(f"生成的关键词: {cleaned_keyword}")
            return cleaned_keyword
        else:
            logger.error("生成关键词失败")
            return ""
    except Exception as e:
        logger.error(f"generate_keyword 函数出错: {str(e)}")
        return ""


def describe_images_with_context(images_with_context: List[Tuple[str, str]], base_dir: str = None, theme: str = None) -> List[Tuple[str, str]]:
    """
    使用 GPT-4 为图片生成描述。
    
    Args:
        images_with_context (List[Tuple[str, str]]): 包含图片URL和上下文的元组列表。
        base_dir (str, optional): 基础目录，用于移动被过滤的图片
        theme (str, optional): 主题名称，用于格式化FILTERED_IMAGE_DIR路径
        
    Returns:
        List[Tuple[str, str]]: 包含图片URL和描述的元组列表。
    """
    descriptions = []
    
    for image_url, context in images_with_context:
        try:
            data = {
                'context': context or "No context available for this image.",
                'image_url': image_url
            }
            
            # 调用 call_llm 获取描述
            description = call_llm(
                api_function='describe-image',
                prompt_data=data,
                using_cache=True,
                max_tokens=None
            )
            
            if description:
                logger.debug(f"为图片 {image_url} 生成的描述:\n{description}")
                descriptions.append((image_url, description))
            else:
                logger.error(f"图片 {image_url} 未能生成描述")
                # 不添加任何默认描述，直接跳过
                
        except ValueError as ve:
            error_msg = str(ve)
            if is_content_filter_error(error_msg):
                logger.error(f"图片 {image_url} 触发了内容过滤，跳过此图片")
                # 移动被过滤的图片到temp目录
                move_filtered_image_to_temp(image_url, base_dir, theme)
            else:
                logger.error(f"处理图片 {image_url} 时发生ValueError错误: {ve}")
            # 不添加默认描述，直接跳过
        except Exception as e:
            error_msg = str(e)
            if is_content_filter_error(error_msg):
                logger.error(f"图片 {image_url} 触发了内容过滤，跳过此图片")
                # 移动被过滤的图片到temp目录
                move_filtered_image_to_temp(image_url, base_dir, theme)
            else:
                logger.error(f"处理图片 {image_url} 时发生错误: {e}")
            # 不添加任何默认描述，直接跳过
            continue
    
    if not descriptions:
        logger.warning("没有成功生成任何图片描述")
    else:
        logger.info(f"成功处理了 {len(descriptions)} 张图片")
    
    return descriptions

def generate_and_save_descriptions(attribute_json_path: str, base_dir: str = None, theme: str = None) -> None:
    """生成并保存图片描述，统一使用绝对路径作为唯一键避免重复"""
    try:
        # 如果没有提供主题名称，尝试从attribute_json_path中提取
        if not theme and attribute_json_path:
            # 尝试从路径中提取主题名称
            match = re.search(r'results/([^/]+)', attribute_json_path)
            if match:
                theme = match.group(1)
                logger.info(f"从路径中提取的主题名称: {theme}")
        
        # 读取现有的JSON文件
        with open(attribute_json_path, 'r', encoding='utf-8') as f:
            images_data = json.load(f)
            
        # 转换所有filepath为绝对路径
        for img in images_data:
            if 'filepath' in img:
                img['filepath'] = os.path.abspath(img['filepath'])
                
        # 准备需要处理的图片列表
        images_to_process = [
            (img['url'], img.get('context', ''))
            for img in images_data
            if 'url' in img and not img.get('description')  # 只处理没有描述的图片
        ]
        
        if not images_to_process:
            logger.info(f"所有图片已有描述或没有有效图片需要处理: {attribute_json_path}")
            return
            
        # 生成描述
        descriptions = describe_images_with_context(images_to_process, base_dir, theme)
        
        # 使用URL映射更新原始数据
        url_to_desc = dict(descriptions)
        for img in images_data:
            if img.get('url') in url_to_desc:
                img['description'] = url_to_desc[img['url']]
                
        # 保存更新后的数据
        # 先创建备份
        backup_path = attribute_json_path + '.backup'
        if os.path.exists(attribute_json_path):
            import shutil
            shutil.copy2(attribute_json_path, backup_path)
            
        # 写入更新后的数据
        with open(attribute_json_path, 'w', encoding='utf-8') as f:
            json.dump(images_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"已为 {len(descriptions)} 张图片生成描述并更新至 {attribute_json_path}")
        logger.info(f"原文件已备份至 {backup_path}")
        
    except Exception as e:
        logger.error(f"生成和保存图片描述时出错: {str(e)}")
        raise

# 添加一个用于处理已有 JSON 文件中被过滤图片的功能
def process_filtered_images_in_json(attribute_json_path: str, base_dir: str = None, theme: str = None) -> None:
    """
    处理 JSON 文件中被标记为内容过滤的图片，将它们移动到 temp 目录
    
    Args:
        attribute_json_path (str): 图片属性JSON文件的路径
        base_dir (str, optional): 基础目录，用于移动被过滤的图片
        theme (str, optional): 主题名称，用于格式化FILTERED_IMAGE_DIR路径
    """
    try:
        # 如果没有提供主题名称，尝试从attribute_json_path中提取
        if not theme and attribute_json_path:
            # 尝试从路径中提取主题名称
            match = re.search(r'results/([^/]+)', attribute_json_path)
            if match:
                theme = match.group(1)
                logger.info(f"从路径中提取的主题名称: {theme}")
        
        # 读取现有的JSON文件
        with open(attribute_json_path, 'r', encoding='utf-8') as f:
            images_data = json.load(f)
        
        filtered_count = 0
        # 查找被标记为过滤的图片
        for img in images_data:
            if img.get('filtered') or (img.get('error') and is_content_filter_error(img.get('error', ''))):
                if move_filtered_image_to_temp(img['url'], base_dir, theme):
                    filtered_count += 1
                    img['moved_to_temp'] = True
        
        if filtered_count > 0:
            # 保存更新后的数据
            with open(attribute_json_path, 'w', encoding='utf-8') as f:
                json.dump(images_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已将 {filtered_count} 张被过滤的图片移动到临时目录并更新JSON")
    
    except Exception as e:
        logger.error(f"处理JSON中被过滤图片时出错: {str(e)}")

# Example usage
if __name__ == "__main__":
    from modules.tavily_search import process_results

    theme = 'example_theme'  # Replace with your theme
    results, images = process_results(theme)
    
    images_with_context = [(img['url'], img['context']) for img in images]
    
    # 获取当前目录作为基础目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(current_dir)  # 上一级目录

    descriptions = describe_images_with_context(images_with_context, base_dir, theme)

    for image_url, desc in descriptions:
        print(f"Image URL: {image_url}\nDescription: {desc}\n")
        
    # 示例：处理特定JSON文件中被过滤的图片
    # json_path = os.path.join(base_dir, "results", theme, "images", f"{theme}_images_metadata.json")
    # process_filtered_images_in_json(json_path, base_dir, theme)