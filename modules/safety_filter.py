import logging
import openai # Add openai import
import os
from typing import Tuple, Dict, List, Optional
# Removed unused import yaml
from openai import OpenAI, AzureOpenAI
import json # Add json import

# Reuse credential loading logic similar to langchain_interface
from modules.gpt_parameters import (
    ENV_AZURE_OPENAI_ENDPOINT, ENV_AZURE_OPENAI_API_KEY,
    DEFAULT_AZURE_ENDPOINT, MODEL_CONFIGS, LLM_PROVIDER_AZURE, DEFAULT_MODEL_KEY,
    LLM_PROVIDER_DEEPSEEK, MODEL_KEY_ARK_DEEPSEEK_R1,LLM_PROVIDER_GOOGLE, MODEL_KEY_GEMINI_25_PRO,MODLE_KEY_GPT_NANO,
    MODEL_KEY_GPT41, M<PERSON>EL_KEY_O3_MINI
)
AZURE_OPENAI_ENDPOINT = os.getenv(ENV_AZURE_OPENAI_ENDPOINT, DEFAULT_AZURE_ENDPOINT)
AZURE_OPENAI_API_KEY = os.getenv(ENV_AZURE_OPENAI_API_KEY)

# Added imports for new logic
from modules.langchain_interface import call_llm_json_response
# REMOVED import for check_safe and the try/except block
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Renamed from rewrite_with_deepseek (internal function)
def _rewrite_with_llm(text: str) -> Tuple[str, bool]:
    """
    Uses DeepSeek LLM to rewrite potentially graphic text into neutral terms.
    Relies on the LLM to provide a gore score.

    Args:
        text: The original text string.

    Returns:
        A tuple containing:
            - The rewritten (neutral) text.
            - A boolean gore_flag (True if the LLM indicates gore with score >= 1).
    """
    if not text:
        return "", False

    try:
        logger.debug(f"Applying DeepSeek gore neutralization to text: '{text}...'")
        # Call the LLM for gore neutralization, expecting neutral_text and gore_score
        response = call_llm_json_response(
            api_function="gore_neutralizer",
            prompt_data={"text": text},
            llm_type=LLM_PROVIDER_DEEPSEEK,
            model_key=MODEL_KEY_ARK_DEEPSEEK_R1,
            using_cache=False
        )

        # --- Start Add Check for None Response ---
        if response is None:
            logger.error(f"LLM call for gore_neutralizer failed to return a valid response for text: '{text[:80]}...'")
            # Fallback: return original text, assume it might be unsafe
            return text, True # Keep gore_flag=True on error
        # --- End Add Check ---

        neutral_text = response.get("neutral_text", text) # Default to original if key missing
        # Get gore_score, default to 1 (assume unsafe) if missing or invalid type
        gore_score = response.get("gore_score", 1)
        if not isinstance(gore_score, int):
            logger.warning(f"Invalid gore_score type received ({type(gore_score)}), defaulting to 1.")
            gore_score = 1

        # gore_flag is True if the score indicates gore (>= 1)
        gore_flag = gore_score >= 1

        if gore_flag and neutral_text == text:
             logger.info(f"DeepSeek flagged text as gore (score: {gore_score}) but did not rewrite: '{text[:80]}...'")
        elif neutral_text != text:
             logger.debug(f"Successfully rewrote text with DeepSeek (score: {gore_score}). Original started with: '{text[:80]}...'")
        # else: text not rewritten and score is 0 - likely already safe

        return neutral_text, gore_flag

    except Exception as e:
        logger.error(f"Error during DeepSeek rewrite for text starting with '{text[:80]}...': {e}", exc_info=True)
        # Fallback: return original text, assume it might be unsafe
        return text, True # Keep gore_flag=True on error

# Renamed from apply_gore_metaphor_replacement
def apply_gore_neutralization(text: str) -> Tuple[str, bool]:
    """
    Applies gore neutralization using DeepSeek LLM based on its gore score.

    Args:
        text: The original text string.

    Returns:
        A tuple containing:
            - The processed (potentially rewritten) text.
            - A boolean flag indicating if gore was detected/rewritten by the LLM (gore_flag).
              The flag is True if the LLM returned a gore_score >= 1.
    """
    if not text:
        return "", False

    original_text = text
    # The gore_flag now comes directly from the LLM's assessment (gore_score >= 1)
    # Calls the renamed internal function
    rewritten_text, llm_indicated_gore = _rewrite_with_llm(original_text)

    if rewritten_text != original_text:
         logger.info(f"Gore replacement applied via LLM from '{original_text}  \n to '{rewritten_text}...'")
    elif llm_indicated_gore:
         # This case means LLM gave score >= 1 but returned original text
         logger.debug(f"DeepSeek indicated gore but returned original text: '{original_text}...'")
  
    # The final flag directly reflects the LLM's gore assessment
    return rewritten_text, llm_indicated_gore



# --- New function to check Azure Content Filter ---
def check_azure_content_filter(text: str) -> bool:
    """
    Checks Azure's built-in content filter by making a minimal chat completion call
    using the project's configured Azure GPT-4.1 model.

    Args:
        text: The text to check.

    Returns:
        True if the prompt content filter flags 'violence' (or other relevant categories)
        as filtered, False otherwise or on error.
    """
    if not text or not text.strip():
        logger.debug("Input text for Azure content filter check is empty.")
        return False

    # Check Azure configuration availability
    if not AZURE_OPENAI_API_KEY or not AZURE_OPENAI_ENDPOINT:
        logger.warning("Azure OpenAI Key or Endpoint not configured. Skipping content filter check.")
        return False

    try:
        # Explicitly use the GPT-4.1 model instead of using the default model
        azure_configs = MODEL_CONFIGS.get(LLM_PROVIDER_AZURE, {})
        filter_model_key = MODLE_KEY_GPT_NANO

        if filter_model_key not in azure_configs:
            logger.error(f"model key '{filter_model_key}' not found in MODEL_CONFIGS. Cannot perform content filter check.")
            return False # Cannot proceed without model config

        model_config = azure_configs[filter_model_key]
        deployment_name = model_config.get('deployment_name', filter_model_key)
        api_version = model_config.get('api_version', "2025-01-01-preview") # Use the API version from the config

        logger.debug(f"Using LLM '{filter_model_key}' to check '{text}' for Azure content filter.")

        # Configure Azure OpenAI Client
        client = openai.AzureOpenAI(
            api_key=AZURE_OPENAI_API_KEY,
            api_version=api_version,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            max_retries=1 # Don't retry heavily for this check
        )

        # Make a minimal chat completion call purely to trigger filtering
        logger.debug(f"Calling Azure ChatCompletions (deployment: {deployment_name}) for content filter check...")
        response = client.chat.completions.create(
            model=deployment_name, # Use 'model' parameter for deployment name here
            messages=[{"role": "user", "content": text}],
            max_tokens=1, # We only need the filter results, not the generation
            temperature=0.0
        )

        # # Debugging: Log the full response structure
        logger.debug(f"Azure response attributes: {vars(response) if hasattr(response, '__dict__') else 'No vars available'}")
        
        # Find prompt_filter_results - check different possible paths
        try:
            # Check for prompt_filter_results directly on response
            if hasattr(response, 'prompt_filter_results'):
                filter_results = response.prompt_filter_results
                #logger.debug(f"Found prompt_filter_results as direct attribute: {filter_results}")
            # Check for dictionary access (response might be a dict or dict-like)
            elif isinstance(response, dict) and 'prompt_filter_results' in response:
                filter_results = response['prompt_filter_results']
                #logger.debug(f"Found prompt_filter_results as dict key: {filter_results}")
            # Check if it's nested under 'choices' (common structure)
            elif hasattr(response, 'choices'):
                #logger.debug(f"Response has 'choices' attribute: {response.choices}")
                # Try to find it in the first choice
                if response.choices and hasattr(response.choices[0], 'prompt_filter_results'):
                    filter_results = response.choices[0].prompt_filter_results
                    #logger.debug(f"Found prompt_filter_results in first choice: {filter_results}")
                # Otherwise, try in the response model root itself
                else:
                    logger.debug("prompt_filter_results not found in choices")
                    filter_results = None
            else:
                logger.debug("Could not locate prompt_filter_results in standard locations")
                filter_results = None
            
            # If we found filter results, check them
            if filter_results:
                #logger.debug(f"Processing found filter_results: {filter_results}")
                
                # Check if filter_results is a list
                if isinstance(filter_results, list):
                    for result in filter_results:
                        # Check if result is a dict - this was the error
                        if isinstance(result, dict):
                            content_filter = result.get('content_filter_results', {})
                            # Or check the violence field directly
                            violence_filter = content_filter.get('violence', {})
                            if violence_filter.get('filtered', False) is True:
                                logger.info(f"Azure content filter flagged text for 'violence' (Severity: {violence_filter.get('severity')}).")
                                return True
                        # Otherwise try attribute access
                        elif hasattr(result, 'content_filter_results'):
                            content_filter = result.content_filter_results
                            if hasattr(content_filter, 'violence'):
                                violence_filter = content_filter.violence
                                if hasattr(violence_filter, 'filtered') and violence_filter.filtered:
                                    logger.info(f"Azure content filter flagged text for 'violence' (Severity: {getattr(violence_filter, 'severity', 'unknown')}).")
                                    return True
                            # Try dict-like access if attributes fail
                            elif hasattr(content_filter, 'get'):
                                violence_filter = content_filter.get('violence', {})
                                if violence_filter.get('filtered', False):
                                    logger.info(f"Azure content filter flagged text for 'violence' (Severity: {violence_filter.get('severity', 'unknown')}).")
                                    return True
                else:
                    logger.warning(f"prompt_filter_results is not a list: {type(filter_results)}")
                    
                # Made it this far without finding a filter hit
                #logger.debug("Azure content filter check passed (no relevant categories flagged).")
                return False
            else:
                logger.warning("Azure response did not contain usable prompt_filter_results.")
                return False
                
        except Exception as filter_processing_error:
            logger.error(f"Error while processing filter results: {filter_processing_error}", exc_info=True)
            return False # Assume safe if we can't process the results

    except openai.APIConnectionError as e:
        logger.error(f"Azure OpenAI API connection error during content filter check: {e}")
        return False # Assume safe on connection errors
    except openai.RateLimitError as e:
        logger.error(f"Azure OpenAI API rate limit exceeded during content filter check: {e}")
        return False # Assume safe if rate limited
    except openai.APIStatusError as e:
        if e.status_code == 400:
            try:
                # Attempt to parse the response body as JSON
                error_data = e.response.json() # Assumes e.response has a .json() method
                error_details = error_data.get('error', {})
                error_code = error_details.get('code')

                # Check if the code indicates content filtering
                if error_code == 'content_filter' or error_code == 'ResponsibleAIPolicyViolation':
                    logger.info(f"Azure content filter flagged text due to API response (400 - {error_code}).")
                    # Optionally log more details from error_details if needed
                    #logger.debug(f"Azure content filter details (from 400 error JSON): {error_details}")
                    return True # Indicate filtering occurred
                else:
                    # 400 error, but not the specific content filter codes we know
                    logger.error(f"Azure OpenAI API status error 400 (code: {error_code or 'unknown'}) during content filter check: {e.response.text}") # Log raw text for debugging
                    return False # Assume safe for other 400s

            except json.JSONDecodeError:
                # Handle cases where the response body is not valid JSON
                logger.error(f"Azure OpenAI API status error 400 with non-JSON response during content filter check: {e.response.text}") # Log raw text
                return False # Assume safe if we can't parse the error
            except Exception as parse_err:
                 # Catch other potential errors during parsing/checking
                 logger.error(f"Error processing Azure 400 error response: {parse_err}", exc_info=True)
                 return False # Assume safe on processing error

        elif e.status_code == 404:
             logger.error(f"Azure OpenAI API status error 404 (Resource Not Found) during content filter check. Deployment '{deployment_name}' likely invalid: {e.response.text}")
             return False # Assume safe on 404
        else:
             # Handle other status errors
             logger.error(f"Azure OpenAI API status error during content filter check: {e.status_code} - {e.response.text}")
             return False # Assume safe on other API errors
    except Exception as e:
        logger.error(f"Unexpected error during Azure content filter check: {e}", exc_info=True)
        return False # Assume safe on unexpected errors


# Example Usage (for testing - needs adjustment for new logic)
if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO) # Enable logging for test

    # Mock langchain_interface for standalone testing (using the renamed internal function)
    def mock_call_llm_json_response(api_function, prompt_data, expected_fields, **kwargs):
        print(f"Mock LLM Call: {api_function} for text: '{prompt_data['text'][:30]}...'")
        original_text = prompt_data['text']
        if "throat cut" in original_text.lower():
            rewritten = original_text.lower().replace("throat cut", "neck injury")
            return {"neutral_text": rewritten, "gore_score": 1}
        if "slashed open" in original_text.lower():
             rewritten = original_text.lower().replace("slashed open", "severely injured")
             return {"neutral_text": rewritten, "gore_score": 2}
        if "brutal murders" in original_text.lower():
             rewritten = original_text.lower().replace("brutal murders", "violent deaths")
             return {"neutral_text": rewritten, "gore_score": 3}
        # Simulate text flagged but not rewritten
        if "savage brutality" in original_text.lower():
             return {"neutral_text": original_text, "gore_score": 4} # High score, no rewrite
        # Safe text
        if "perfectly safe" in original_text.lower():
             return {"neutral_text": original_text, "gore_score": 0}
        # Default fallback (unknown text)
        return {"neutral_text": original_text, "gore_score": 0}

    # REMOVED mock_check_safe function

    # Replace real functions with mocks for testing
    call_llm_json_response = mock_call_llm_json_response
    # No need to replace check_safe anymore

    test_sentences = [
        "Their throats were cut, their abdomens slashed open, and some internal organs were removed.",
        "The first victim was Elizabeth Stride. She was found with only her throat cut her body was not otherwise mutilated.",
        "Catherine Eddowes, a forty-six-year-old street vendor and part-time prostitute, was murdered with savage brutality.", # Should flag, not rewrite
        "A series of eleven brutal murders occurred.", # Should rewrite and flag
        "This text is perfectly safe." # Should not rewrite, not flag
    ]

    print("\n--- Testing Gore Replacement with Mocked DeepSeek (using gore_score) ---")
    for sentence in test_sentences:
        processed, flag = apply_gore_neutralization(sentence) # Call the renamed public function
        print(f"Original:  {sentence}")
        print(f"Processed: {processed} (Gore Flag from LLM: {flag})") # Updated label
        print("-" * 20)

# Removed old test sentences and print statements 
