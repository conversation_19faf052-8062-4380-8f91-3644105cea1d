# modules/text_preprocessing.py

import os
import re
import argparse
import tiktoken  # Used for token counting
import logging
from modules.nlp_model import (
    get_nlp_model,
    SUPPORTED_LANGUAGES,
    LANGUAGE_CODES
)
from config import logger
from typing import List, Dict, Optional, Union
from langdetect import detect as detect_language  # 添加导入
from modules.langchain_interface import call_llm_json_response

def generate_keywords(script_file: str, language: str = "English") -> Dict[str, Union[str, List[str]]]:
    """
    根据脚本文件生成多个关键词，包括一个主题关键词和多个不同角度的关键词
    
    Args:
        script_file (str): 脚本文件路径
        language (str): 生成关键词的语言
    
    Returns:
        Dict[str, Union[str, List[str]]]: 包含主题关键词和多个关键词的字典
            {
                "topic": "主题关键词",
                "keywords": ["关键词1", "关键词2", ...]
            }
    """
    try:
        # 读取脚本文件内容
        with open(script_file, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 准备输入数据
        input_data = {
            "script": script_content,
            "language": language
        }
        
        # 调用LLM生成关键词
        
        result = call_llm_json_response(
            api_function="gen_keywords",
            prompt_data=input_data,
            expected_fields=["topic", "keywords"],
            using_cache=False   
        )
        
        if result and "topic" in result and "keywords" in result:
            # 清理关键词（去除特殊符号）
            topic = re.sub(r'[^\w\s]', '', result["topic"])
            keywords = [re.sub(r'[^\w\s]', '', kw) for kw in result["keywords"]]
            
            logger.info(f"生成的主题关键词: {topic}")
            logger.info(f"生成的额外关键词: {', '.join(keywords)}")
            
            return {
                "topic": topic,
                "keywords": keywords
            }
        else:
            logger.error("生成关键词失败")
            raise ValueError("LLM未返回预期的关键词结果")
            
    except Exception as e:
        logger.error(f"generate_keywords 函数出错: {str(e)}")
        raise


def split_text_into_chapters(text: str) -> List[str]:
    """按章节智能分割中文小说文本
    
    Args:
        text: 原始文本字符串
    Returns:
        章节列表,每个元素为一个完整章节的内容
    """
    # 匹配更多中文章节标题格式
    chapter_patterns = [
        r'第[零一二三四五六七八九十百千万]+[章节回][\s　]*.*?\n',  # 中文数字章节(带标题)
        r'第\d+[章节回][\s　]*.*?\n',  # 阿拉伯数字章节(带标题)
        r'Chapter\s+\d+.*?\n',  # 英文章节
        r'^\s*\d+\s*$\n'  # 独立数字章节
    ]
    
    # 组合所有模式
    pattern = '|'.join(chapter_patterns)
    
    # 按章节分割
    chapters = []
    current_chapter = []
    
    for line in text.split('\n'):
        if any(re.match(p, line + '\n') for p in chapter_patterns):
            if current_chapter:
                chapters.append('\n'.join(current_chapter))
            current_chapter = [line]
        else:
            current_chapter.append(line)
            
    # 添加最后一章
    if current_chapter:
        chapters.append('\n'.join(current_chapter))
        
    logging.info(f"Split text into {len(chapters)} chapters")
    return chapters

def split_text_for_analysis(text: str, max_tokens: int = 9000) -> List[str]:
    """将文本分割成适合分析的块
    
    Args:
        text: 原始文本
        max_tokens: 每块最大token数
    Returns:
        文本块列表
    """
    # 先按章节分
    chapters = split_text_into_chapters(text)
    tokenizer = tiktoken.get_encoding('cl100k_base')
    chunks = []
    
    i = 0
    while i < len(chapters):
        chapter = chapters[i]
        tokens = tokenizer.encode(chapter)
        
        # 处理小章节的情况
        if len(tokens) < max_tokens // 2:
            # 检查是否还有下一章
            if i + 1 < len(chapters):
                next_chapter = chapters[i + 1]
                next_tokens = tokenizer.encode(next_chapter)
                
                # 如果当前章节加下一章不超过max_tokens
                if len(tokens) + len(next_tokens) <= max_tokens:
                    # 合并章节
                    chapter = chapter + "\n\n" + next_chapter
                    tokens = tokenizer.encode(chapter)
                    i += 1  # 跳过下一章
                # 如果超过max_tokens，当前小章节独立成chunk
            
            chunks.append(chapter)
            i += 1
            continue
            
        # 处理大章节的情况
        if len(tokens) > max_tokens:
            paras = re.split(r'\n+', chapter)
            current_chunk = []
            current_length = 0
            
            for para in paras:
                para_tokens = tokenizer.encode(para)
                if current_length + len(para_tokens) > max_tokens:
                    if current_chunk:
                        chunks.append('\n'.join(current_chunk))
                    current_chunk = [para]
                    current_length = len(para_tokens)
                else:
                    current_chunk.append(para)
                    current_length += len(para_tokens)
                    
            if current_chunk:
                chunks.append('\n'.join(current_chunk))
        else:
            chunks.append(chapter)
            
        i += 1
            
    logging.info(f"Split text into {len(chunks)} chunks for analysis")
    return chunks

def smart_paragraph_split(
    text: str,
    min_sentences: int = 1,
    max_sentences: int = 2,
    language: str = 'English'
) -> List[str]:
    # 将语言名称转换为标题格式（每个单词首字母大写）
    language = language.title()
   
    if language not in SUPPORTED_LANGUAGES:
        logger.warning(f"smart_paragraph_split: 不支持的语言: {language}，使用默认英语")
        language = 'English'

    # 使用 get_nlp_model 函数获取适当的 NLP 模型
    nlp = get_nlp_model(lang=language)
    
    doc = nlp(text)

    sentences = [sent.text.strip() for sent in doc.sents if sent.text.strip()]

    result_paragraphs = []
    current_paragraph = []

    for sentence in sentences:
        current_paragraph.append(sentence)
        if len(current_paragraph) >= max_sentences:
            paragraph = ''.join(current_paragraph) if language in ['chinese', 'japanese'] else ' '.join(current_paragraph)
            result_paragraphs.append(paragraph)
            current_paragraph = []

    if current_paragraph:
        paragraph = ''.join(current_paragraph) if language in ['chinese', 'japanese'] else ' '.join(current_paragraph)
        result_paragraphs.append(paragraph)

    return result_paragraphs

def split_into_sentences(content: str, language: Optional[str] = None) -> List[str]:
    """根据不同语言使用不同的分句策略"""
    if not content.strip():
        return []

    current_lang = language.lower() if language else detect_language(content)
    logger.debug(f"检测到的语言: {current_lang}")

    try:
        # 获取语言模型
        language_name = next((name for name, code in LANGUAGE_CODES.items() 
                            if code == current_lang and not name.startswith('zh-') 
                            and name not in ['中文']), 'English')
        nlp = get_nlp_model(lang=language_name)
        
        # 中文使用混合策略
        if current_lang in ['zh', 'chinese']:
            # 使用更清晰的标点符号模式
            pattern = r'([。！？；])'
            segments = re.split(pattern, content)
            
            # 重组带标点的句子
            raw_sentences = []
            for i in range(0, len(segments)-1, 2):
                if i+1 < len(segments):
                    sentence = segments[i] + segments[i+1]
                    raw_sentences.append(sentence)
            if len(segments) % 2 == 1:
                raw_sentences.append(segments[-1])
                
            # 使用 spacy 进行细分
            refined_sentences = []
            for raw_sentence in raw_sentences:
                if raw_sentence.strip():
                    doc = nlp(raw_sentence)
                    for sent in doc.sents:
                        text = sent.text.strip()
                        if text:
                            refined_sentences.append(text)
            
            return [s for s in refined_sentences if len(s) > 1]
        
        # 其他语言直接使用 spacy
        else:
            doc = nlp(content)
            return [sent.text.strip() for sent in doc.sents if sent.text.strip()]

    except Exception as e:
        logger.error(f"分句失败: {str(e)}")
        # 降级方案：使用基础正则表达式
        if current_lang in ['zh', 'chinese']:
            pattern = r'(?<=[。！？；])\s*'
        else:
            pattern = r'(?<=[.!?])\s+'
        sentences = [s.strip() for s in re.split(pattern, content) if s.strip()]
        return sentences if sentences else [content.strip()]
 
    """
    将文本按句子分割，并根据长度合并或分割
    
    Args:
        content: 要分割的文本
        min_length: 最小句子长度
        max_length: 最大句子长度
    
    Returns:
        List[str]: 分割后的句子列表
    """
    if not content or not isinstance(content, str):
        logger.warning("输入文本为空或非字符串类型")
        return []

    try:
        # 检测语言
        try:
            lang = detect_language(content[:1000])
            logger.debug(f"检测到语言: {lang}")
        except:
            lang = 'en'
            logger.warning("语言检测失败，默认使用英语分句规则")

        # 根据语言选择分句规则
        if lang.startswith('zh'):
            # 中文分句规则 - 使用更清晰的标点符号模式
            pattern = r'([。！？；])'  # 简化引号和括号的使用
        else:
            # 其他语言分句规则
            pattern = r'([.!?][\""\')\]}}>]*)'

        # 分割句子
        segments = re.split(pattern, content)
        sentences = []
        
        # 重组句子（保留标点）
        for i in range(0, len(segments)-1, 2):
            if segments[i]:
                sentence = segments[i] + (segments[i+1] if i+1 < len(segments) else '')
                sentences.append(sentence.strip())
        
        # 处理最后一个片段
        if len(segments) % 2 == 1 and segments[-1].strip():
            sentences.append(segments[-1].strip())

        # 合并过短的句子
        merged_sentences = []
        current = []
        current_length = 0
        
        for sent in sentences:
            if not sent.strip():
                continue
                
            if current_length + len(sent) <= max_length:
                current.append(sent)
                current_length += len(sent)
            else:
                if current:
                    merged_sentences.append(' '.join(current))
                current = [sent]
                current_length = len(sent)

        # 添加最后一组
        if current:
            merged_sentences.append(' '.join(current))

        # 处理过长的句子
        final_sentences = []
        for sent in merged_sentences:
            if len(sent) > max_length:
                # 按逗号分割过长的句子
                subsents = re.split(r'[,，;；]', sent)
                current = []
                current_length = 0
                
                for subsent in subsents:
                    if current_length + len(subsent) <= max_length:
                        current.append(subsent)
                        current_length += len(subsent)
                    else:
                        if current:
                            final_sentences.append('，'.join(current))
                        current = [subsent]
                        current_length = len(subsent)
                
                if current:
                    final_sentences.append('，'.join(current))
            else:
                final_sentences.append(sent)

        # 确保所有句子都符合长度要求
        result = [sent for sent in final_sentences if len(sent) >= min_length]
        
        # 如果没有得到任何有效句子，返回原文本
        if not result and content.strip():
            logger.warning("分句结果为空，将整个文本作为一个句子返回")
            return [content.strip()]

        return result

    except Exception as e:
        logger.error(f"分句过程出错: {e}")
        return [content.strip()]