#!/usr/bin/env python3
"""
Test script to verify the fallback optimization works correctly.
"""

import subprocess
import sys
import os
import json
from analyze_assignment_results import analyze_assignment_results

def test_without_generate_image():
    """Test fallback assignment without --generate_image flag."""

    print("=== Testing Fallback Assignment (without --generate_image) ===")

    # Backup the original results
    original_file = "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_images.json"
    backup_file = original_file + ".fallback_test_backup"

    if os.path.exists(original_file):
        print(f"Backing up original results to {backup_file}")
        subprocess.run(["cp", original_file, backup_file], check=True)

    # Run assignment WITHOUT --generate_image flag
    cmd = [
        "python3", "assign_clip_image.py",
        "--json", "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_script.json",
        "--image_dir", "1-theme-talker/results/Putten_murder_case_Netherlands/images",
        "--clips_json", "1-theme-talker/results/Putten_murder_case_Netherlands/assigned_clips.json",
        "--theme", "Putten_murder_case_Netherlands",
        "--reranker-threshold", "0.10"
        # Note: NO --generate_image flag
    ]

    print(f"Running command: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

        if result.returncode != 0:
            print(f"Assignment failed with return code {result.returncode}")
            print("STDOUT:", result.stdout[-1000:])
            print("STDERR:", result.stderr[-1000:])
            return False

        print("Assignment completed successfully!")

        # Analyze the new results
        if os.path.exists(original_file):
            print("\n=== Analyzing Fallback Assignment Results ===")
            results = analyze_assignment_results(original_file)

            print("\n=== Expected Improvements ===")
            print(f"Pending assignments: {results['segments_with_pending']} (should be much lower than 13)")
            print(f"Fallback assignments: {results['segments_with_fallback']} (should be > 0)")
            print(f"Duplicate images: {results['duplicate_images']} (should remain 0 or very low)")
            print(f"Low similarity assignments: {results['low_similarity_count']} (may be higher due to relaxed threshold)")

            # Check if 100% coverage is achieved
            if results['segments_with_pending'] == 0:
                print("✅ 100% COVERAGE ACHIEVED!")
                print("✅ FALLBACK ASSIGNMENT WORKING PERFECTLY!")
                return True
            elif results['segments_with_pending'] < 3:
                print("✅ FALLBACK ASSIGNMENT MOSTLY WORKING!")
                print(f"Only {results['segments_with_pending']} segments remain unassigned")
                return True
            else:
                print("❌ Fallback assignment needs further tuning")
                print(f"Still {results['segments_with_pending']} segments unassigned")
                return False
        else:
            print("❌ Results file not found after assignment")
            return False

    except subprocess.TimeoutExpired:
        print("❌ Assignment timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running assignment: {e}")
        return False

def test_with_generate_image():
    """Test placeholder generation with --generate_image flag."""

    print("\n=== Testing Placeholder Generation (with --generate_image) ===")

    # Backup the original results
    original_file = "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_images.json"
    backup_file = original_file + ".generate_test_backup"

    if os.path.exists(original_file):
        print(f"Backing up original results to {backup_file}")
        subprocess.run(["cp", original_file, backup_file], check=True)

    # Run assignment WITH --generate_image flag
    cmd = [
        "python3", "assign_clip_image.py",
        "--json", "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_script.json",
        "--image_dir", "1-theme-talker/results/Putten_murder_case_Netherlands/images",
        "--clips_json", "1-theme-talker/results/Putten_murder_case_Netherlands/assigned_clips.json",
        "--theme", "Putten_murder_case_Netherlands",
        "--reranker-threshold", "0.10",
        "--generate_image"  # Enable image generation
    ]

    print(f"Running command: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

        if result.returncode != 0:
            print(f"Assignment failed with return code {result.returncode}")
            print("STDOUT:", result.stdout[-1000:])
            print("STDERR:", result.stderr[-1000:])
            return False

        print("Assignment completed successfully!")

        # Analyze the results for placeholder generation
        if os.path.exists(original_file):
            print("\n=== Analyzing Placeholder Generation Results ===")

            with open(original_file, 'r') as f:
                data = json.load(f)

            # Count placeholders
            placeholder_count = 0
            generation_required_count = 0

            for segment in data:
                media = segment.get('assigned_media', {})
                if media.get('type') == 'pending':
                    placeholder_count += 1
                    if media.get('generation_required'):
                        generation_required_count += 1

            print(f"Placeholder segments: {placeholder_count}")
            print(f"Segments marked for generation: {generation_required_count}")

            if generation_required_count > 0:
                print("✅ PLACEHOLDER GENERATION WORKING!")
                print(f"Ready for image_generation.py to process {generation_required_count} segments")
                return True
            else:
                print("❌ No segments marked for generation")
                return False
        else:
            print("❌ Results file not found after assignment")
            return False

    except subprocess.TimeoutExpired:
        print("❌ Assignment timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running assignment: {e}")
        return False

def restore_original():
    """Restore the original results."""
    original_file = "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_images.json"

    # Look for any backup file to restore
    backup_files = [
        original_file + ".fallback_test_backup",
        original_file + ".generate_test_backup",
        original_file + ".backup"
    ]

    for backup_file in backup_files:
        if os.path.exists(backup_file):
            print(f"Restoring original results from {backup_file}")
            subprocess.run(["cp", backup_file, original_file], check=True)
            print("Original results restored")
            return

    print("No backup file found to restore")

if __name__ == "__main__":
    print("🚀 Testing Fallback Optimization")
    print("This will test both fallback assignment and placeholder generation strategies")

    # Test 1: Fallback assignment without generate_image
    fallback_success = test_without_generate_image()

    # Test 2: Placeholder generation with generate_image
    placeholder_success = test_with_generate_image()

    # Summary
    print("\n" + "="*60)
    print("OPTIMIZATION TEST SUMMARY")
    print("="*60)

    if fallback_success:
        print("✅ Fallback Assignment: WORKING")
        print("   - Reduced pending assignments significantly")
        print("   - Used relaxed similarity thresholds effectively")
    else:
        print("❌ Fallback Assignment: NEEDS IMPROVEMENT")

    if placeholder_success:
        print("✅ Placeholder Generation: WORKING")
        print("   - Created proper placeholders for image_generation.py")
        print("   - Marked segments with generation_required flag")
    else:
        print("❌ Placeholder Generation: NEEDS IMPROVEMENT")

    if fallback_success and placeholder_success:
        print("\n🎉 ALL OPTIMIZATIONS WORKING CORRECTLY!")
        print("The system now properly handles both scenarios:")
        print("1. --generate_image: Creates placeholders for AI generation")
        print("2. No --generate_image: Uses fallback assignment with relaxed constraints")
    else:
        print("\n⚠️  Some optimizations need further tuning")

    # Restore original state
    restore_original()

    sys.exit(0 if (fallback_success and placeholder_success) else 1)
