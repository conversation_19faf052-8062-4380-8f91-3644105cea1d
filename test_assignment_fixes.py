#!/usr/bin/env python3
"""
Test script to verify the assignment fixes work correctly.
"""

import subprocess
import sys
import os
import json
from analyze_assignment_results import analyze_assignment_results

def run_assignment_test():
    """Run the assignment with our fixes and analyze results."""
    
    print("=== Testing Assignment Fixes ===")
    
    # Backup the original results
    original_file = "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_images.json"
    backup_file = original_file + ".backup"
    
    if os.path.exists(original_file):
        print(f"Backing up original results to {backup_file}")
        subprocess.run(["cp", original_file, backup_file], check=True)
    
    # Run the assignment with our fixes
    cmd = [
        "python3", "assign_clip_image.py",
        "--json", "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_script.json",
        "--image_dir", "1-theme-talker/results/Putten_murder_case_Netherlands/images",
        "--clips_json", "1-theme-talker/results/Putten_murder_case_Netherlands/assigned_clips.json",
        "--theme", "Putten_murder_case_Netherlands",
        "--reranker-threshold", "0.15"  # Set a reasonable threshold
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode != 0:
            print(f"Assignment failed with return code {result.returncode}")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
        
        print("Assignment completed successfully!")
        print("STDOUT:", result.stdout[-1000:])  # Show last 1000 chars
        
        # Analyze the new results
        if os.path.exists(original_file):
            print("\n=== Analyzing New Results ===")
            new_results = analyze_assignment_results(original_file)
            
            print("\n=== Comparison with Expected Improvements ===")
            print(f"Duplicate images: {new_results['duplicate_images']} (should be 0 or very low)")
            print(f"Low similarity assignments: {new_results['low_similarity_count']} (should be much lower)")
            print(f"Fallback assignments: {new_results['segments_with_fallback']} (should be lower)")
            
            # Check if we improved
            if new_results['duplicate_images'] <= 2 and new_results['low_similarity_count'] <= 5:
                print("✅ IMPROVEMENT ACHIEVED!")
                return True
            else:
                print("❌ Still has issues, may need further fixes")
                return False
        else:
            print("❌ Results file not found after assignment")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Assignment timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running assignment: {e}")
        return False

def restore_backup():
    """Restore the original results from backup."""
    original_file = "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_images.json"
    backup_file = original_file + ".backup"
    
    if os.path.exists(backup_file):
        print(f"Restoring original results from {backup_file}")
        subprocess.run(["cp", backup_file, original_file], check=True)
        print("Original results restored")
    else:
        print("No backup file found")

if __name__ == "__main__":
    success = run_assignment_test()
    
    if not success:
        print("\n=== Test failed, restoring backup ===")
        restore_backup()
        sys.exit(1)
    else:
        print("\n=== Test passed! Fixes are working ===")
        sys.exit(0)
