#!/usr/bin/env python3
"""
Analyze the assignment results to identify issues with image reuse and similarity scores.
"""

import json
from collections import defaultdict
import os

def analyze_assignment_results(json_file_path):
    """Analyze the assignment results and identify issues."""

    # Load the results file
    with open(json_file_path, 'r') as f:
        data = json.load(f)

    # Track image usage
    image_usage = defaultdict(list)
    total_segments = len(data)
    segments_with_clips = 0
    segments_with_images = 0
    segments_with_multi_images = 0
    segments_with_fallback = 0
    low_similarity_count = 0
    segments_with_pending = 0

    print('=== ASSIGNMENT ANALYSIS ===')
    print(f'Total segments: {total_segments}')

    for segment in data:
        segment_id = segment['segment_id']
        media = segment.get('assigned_media', {})
        media_type = media.get('type')

        if media_type == 'clip':
            segments_with_clips += 1
        elif media_type == 'image':
            segments_with_images += 1
            filepath = media.get('filepath')
            similarity = media.get('similarity', 0)
            if filepath:
                image_usage[filepath].append((segment_id, similarity))
            if similarity < 0.3:
                low_similarity_count += 1
        elif media_type == 'multi_image':
            segments_with_multi_images += 1
            filepaths = media.get('filepaths', [])
            similarity = media.get('similarity', 0)
            for filepath in filepaths:
                image_usage[filepath].append((segment_id, similarity))
            if similarity < 0.3:
                low_similarity_count += 1
            # Check for fallback assignment
            if media.get('fallback_assignment'):
                segments_with_fallback += 1
        elif media_type in ['pending', 'multi_image_pending']:
            segments_with_pending += 1
            # Also track any existing filepaths in pending assignments
            filepaths = media.get('filepaths', [])
            similarity = media.get('similarity', 0)
            for filepath in filepaths:
                image_usage[filepath].append((segment_id, similarity))

    print(f'Segments with clips: {segments_with_clips}')
    print(f'Segments with single images: {segments_with_images}')
    print(f'Segments with multi images: {segments_with_multi_images}')
    print(f'Segments with pending assignments: {segments_with_pending}')
    print(f'Segments with fallback assignment: {segments_with_fallback}')
    print(f'Segments with low similarity (<0.3): {low_similarity_count}')

    print('\n=== IMAGE REUSE ANALYSIS ===')
    duplicate_images = {img: usage for img, usage in image_usage.items() if len(usage) > 1}
    print(f'Total unique images used: {len(image_usage)}')
    print(f'Images used multiple times: {len(duplicate_images)}')

    if duplicate_images:
        print('\nDuplicate image usage:')
        for img_path, usage in duplicate_images.items():
            img_name = os.path.basename(img_path)
            print(f'  {img_name}: used {len(usage)} times')
            for segment_id, similarity in usage:
                print(f'    - Segment {segment_id}: similarity {similarity:.4f}')

    print('\n=== LOW SIMILARITY ANALYSIS ===')
    low_sim_segments = []
    for segment in data:
        media = segment.get('assigned_media', {})
        similarity = media.get('similarity', 0)
        if similarity < 0.3 and media.get('type') in ['image', 'multi_image']:
            low_sim_segments.append((segment['segment_id'], similarity, media.get('type')))

    if low_sim_segments:
        print(f'Segments with similarity < 0.3:')
        for segment_id, similarity, media_type in low_sim_segments:
            print(f'  Segment {segment_id}: {similarity:.4f} ({media_type})')

    print('\n=== ROUND ANALYSIS ===')
    round_stats = defaultdict(int)
    for segment in data:
        media = segment.get('assigned_media', {})
        source_round = media.get('source_round', 'unknown')
        round_stats[source_round] += 1

    for round_num, count in sorted(round_stats.items(), key=lambda x: str(x[0])):
        print(f'Round {round_num}: {count} segments')

    return {
        'total_segments': total_segments,
        'duplicate_images': len(duplicate_images),
        'low_similarity_count': low_similarity_count,
        'segments_with_fallback': segments_with_fallback,
        'segments_with_pending': segments_with_pending
    }

if __name__ == "__main__":
    json_file = "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_images.json"
    results = analyze_assignment_results(json_file)

    print('\n=== SUMMARY ===')
    print(f'Issues found:')
    print(f'- Duplicate image usage: {results["duplicate_images"]} images')
    print(f'- Low similarity assignments: {results["low_similarity_count"]} segments')
    print(f'- Fallback assignments: {results["segments_with_fallback"]} segments')
    print(f'- Pending assignments: {results["segments_with_pending"]} segments')
