#!/usr/bin/env python3
# image_generation.py - 为段落生成图片的模块

import os
import re
import sys
import json
import argparse
from typing import Dict, Any, List, Optional, Tuple, Union
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field # Updated import
import copy

from modules.image_prompt_generation import generate_prompts_for_paragraphs
from config import get_param
import logging
import tempfile
import shutil

# 导入新的 comfyui 客户端模块
from modules import comfyui_client

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
# 全局配置
SECONDS_PER_IMAGE = 8 # Keep if still used for estimation, otherwise remove
IMAGE_GENERATE_DEBUG = True # Default to False, controlled by CLI arg

# --------------- 数据结构 -----------------
@dataclass
class ImageNeed:
    paragraph_idx: int
    paragraph: dict # Reference to the original paragraph dict
    need: int             # 实际需要的图片总数
    existing_paths: list[str]

@dataclass
class TaskPlan: # New dataclass for planning
    para_idx: int
    prompt_item: dict # The specific prompt dict for this task
    expected_path: str
    prompt_seq: int   # 0-based index for the prompt/image within the paragraph
    image_sequence_index_1based: int # 1-based sequence for filename

@dataclass
class TaskInfo: # Kept for tracking submitted tasks
    para_idx: int
    prompt_id: str
    expected_path: str
    prompt_seq: int          # 0-based index for the prompt/image within the paragraph
# --------------------------------------------------


# ===== New/Refactored Helper Functions Start =====

# --- Calculation & Decision Helpers ---

def calculate_num_images(paragraph: dict) -> int:
    """
    给定段落，返回需要的图片数。
    优先取 assigned_media.num_images_needed，其次取顶层 num_images_needed。
    若均无或无效，则返回 1。
    """
    assigned_media = paragraph.get('assigned_media', {})
    # Allow 0 as a valid value if explicitly set? Plan says return 1 if None.
    # Let's stick to the plan: default 1 if not specified or not valid int > 0.
    assigned_needed = assigned_media.get('num_images_needed')
    top_level_needed = paragraph.get('num_images_needed')

    final_needed = 1 # Default value

    # Check assigned_media first
    if isinstance(assigned_needed, int) and assigned_needed >= 0: # Allow 0? Let's refine to >= 1 based on _calculate_real... logic
        final_needed = assigned_needed if assigned_needed > 0 else 1
    # Check top-level next
    elif isinstance(top_level_needed, int) and top_level_needed >= 0:
        final_needed = top_level_needed if top_level_needed > 0 else 1
    # If both exist, original logic took max(1, top, assigned), let's retain max logic if both defined
    elif isinstance(assigned_needed, int) and assigned_needed > 0 and \
         isinstance(top_level_needed, int) and top_level_needed > 0:
         final_needed = max(assigned_needed, top_level_needed)

    return max(1, final_needed) # Ensure at least 1


def is_excluded_media(paragraph: dict) -> bool:
    """检查段落 assigned_media 类型是否为明确排除生成的类型"""
    assigned_media = paragraph.get('assigned_media', {})
    media_type = assigned_media.get('type')
    para_idx = paragraph.get('paragraph_idx', '未知') # Use index added during scan
    para_id_for_log = paragraph.get('segment_id', f"索引 {para_idx}")

    if media_type in ['clip', 'anchor']:
        logger.debug(f"段落 {para_id_for_log}: 类型为 {media_type}，排除生成。")
        return True
    # Plan implicitly excludes final image types from *generation*, treat them as excluded here.
    if media_type in ['image', 'multi_image']:
        logger.debug(f"段落 {para_id_for_log}: 类型为 {media_type} (最终状态)，排除生成。")
        return True
    return False

def has_pending_requirements(paragraph: dict, num_needed: int, num_existing: int, flags: dict) -> bool:
    """检查段落是否处于需要处理的 pending 状态，并考虑强制生成标志"""
    assigned_media = paragraph.get('assigned_media', {})
    media_type = assigned_media.get('type')
    needs_gen_flag = assigned_media.get('needs_generated_image', False)
    should_force_generation = flags.get('should_force_generation', False)
    para_idx = paragraph.get('paragraph_idx', '未知')
    para_id_for_log = paragraph.get('segment_id', f"索引 {para_idx}")

    is_placeholder_gen = (media_type == 'placeholder' and assigned_media.get('status') == 'to_be_generated')
    # Consider 'pending' and 'multi_image_pending' only if needs_generated_image is true
    is_pending_gen = (media_type == 'pending' and needs_gen_flag)
    is_multi_pending_gen = (media_type == 'multi_image_pending' and needs_gen_flag)

    if is_placeholder_gen or is_pending_gen or is_multi_pending_gen:
        # This state requires processing if images aren't sufficient OR forced
        if num_existing < num_needed:
            logger.info(f"段落 {para_id_for_log}: 状态为 {media_type}/pending 且图片不足 ({num_existing}/{num_needed})，有挂起需求。")
            return True
        elif should_force_generation:
             logger.info(f"段落 {para_id_for_log}: 状态为 {media_type}/pending，图片足够 ({num_existing}/{num_needed})，但强制生成，有挂起需求。")
             return True
        else:
             logger.debug(f"段落 {para_id_for_log}: 状态为 {media_type}/pending，图片足够 ({num_existing}/{num_needed}) 且非强制，无显式挂起需求（不重新生成）。")
             return False
    else:
        # Handle cases like pending/multi_pending where needs_generated_image is False explicitly
        if media_type in ['pending', 'multi_image_pending'] and not needs_gen_flag:
            logger.debug(f"段落 {para_id_for_log}: 状态为 {media_type} 但 needs_generated_image=False，无挂起需求。")
            return False

    return False # Other states don't represent a pending requirement

def should_generate(paragraph: dict, num_needed: int, num_existing: int, flags: dict) -> bool:
    """
    根据段落状态和标志决定是否需要生成图片。
    """
    para_idx = paragraph.get('paragraph_idx', '未知')
    para_id_for_log = paragraph.get('segment_id', f"索引 {para_idx}")

    # 1. Check for explicitly excluded types (clip, anchor, image, multi_image)
    if is_excluded_media(paragraph):
        return False

    # 2. Check for pending requirements (placeholder, pending/multi_pending with needs_gen_flag=True)
    #    These states trigger generation if images are missing OR force=True
    if has_pending_requirements(paragraph, num_needed, num_existing, flags):
        return True

    # 3. Fallback for other states (e.g., type missing, or other types not handled above)
    #    Generate only if image count is insufficient. Force flag doesn't apply here unless it was a pending state.
    needs_generation_due_to_count = num_existing < num_needed
    if needs_generation_due_to_count:
         media_type = paragraph.get('assigned_media', {}).get('type', '未知')
         logger.info(f"段落 {para_id_for_log}: 状态非排除/非pending ({media_type})，但图片不足 ({num_existing}/{num_needed})，需要生成。")
    else:
         logger.debug(f"段落 {para_id_for_log}: 状态非排除/非pending，且图片足够 ({num_existing}/{num_needed})，默认不需要生成。")

    return needs_generation_due_to_count


# --- Scanning Functions ---

def _scan_existing_images(theme: str, para_idx: int, num_images_needed: int, image_dir: str, assigned_media: dict) -> list[str]:
    """(内部辅助) 扫描给定段落的本地和 JSON 中指定的现有图片文件"""
    existing_image_paths = []
    para_id_for_log = assigned_media.get('segment_id', f"索引 {para_idx}") # Use passed index

    # Ensure image_dir exists before scanning
    os.makedirs(image_dir, exist_ok=True)

    # Check local files based on naming convention
    try:
        if num_images_needed == 1:
            image_filename = f"{theme}_{para_idx+1}.png"
            image_path = Path(image_dir) / image_filename
            if image_path.is_file() and image_path.stat().st_size > 0:
                existing_image_paths.append(str(image_path.resolve()))
        else:
            # Check files sequentially up to num_images_needed
            for img_seq in range(num_images_needed):
                image_filename = f"{theme}_{para_idx+1}_{img_seq+1}.png"
                image_path = Path(image_dir) / image_filename
                if image_path.is_file() and image_path.stat().st_size > 0:
                    existing_image_paths.append(str(image_path.resolve()))
                # else: # Don't break, collect all that exist up to num_needed
                #     logger.debug(f"段落 {para_id_for_log}: 本地图片 {img_seq+1} 未找到或为空。")
    except OSError as e:
        logger.warning(f"扫描本地图片时出错 (段落 {para_id_for_log}): {e}")


    # Consolidate with filepaths from JSON (assigned_media['filepaths'])
    json_filepaths = assigned_media.get('filepaths', [])
    valid_abs_json_paths = []
    if isinstance(json_filepaths, list):
        for fp in json_filepaths:
            if fp and isinstance(fp, str):
                try:
                    p = Path(fp)
                    # Check if it exists relative to CWD or as absolute
                    abs_p = p.resolve() # Resolves relative paths too
                    if abs_p.is_file() and abs_p.stat().st_size > 0:
                         valid_abs_json_paths.append(str(abs_p))
                    # else: logger.debug(f"JSON path {fp} (resolved: {abs_p}) not found or empty.")
                except Exception as e:
                     logger.warning(f"检查 JSON 文件路径 '{fp}' 时出错: {e}")

    # Merge local scan and validated JSON paths, prioritize local, keep order roughly
    combined_paths = existing_image_paths + [p for p in valid_abs_json_paths if p not in existing_image_paths]

    # Sort based on filename convention (_1, _1_1, _1_2, _2 etc.) and limit
    def sort_key(p):
        match = re.findall(r'_(\d+)(?:_(\d+))?\.png$', Path(p).name)
        if match:
            g1 = int(match[0][0]) if match[0][0] else 0
            g2 = int(match[0][1]) if match[0][1] else 0 # Treat single as _N_0 for sorting? or _N_1? Let's use 1 for 1-based index.
            return (g1, g2 if g2 > 0 else 1) # Treat theme_N.png as theme_N_1 for sort order
        else:
            return (float('inf'), float('inf')) # Non-matching names last

    combined_paths.sort(key=sort_key)

    # Return only up to num_images_needed
    return combined_paths[:num_images_needed]


def scan_needs(paragraphs: list[dict], theme: str, image_dir: str, flags: dict) -> list[ImageNeed]:
    """
    遍历段落，识别需要生成图片的段落，返回 ImageNeed 列表。
    """
    image_needs = []
    for idx, paragraph in enumerate(paragraphs):
        # Add index to paragraph dict for easier reference later
        paragraph['paragraph_idx'] = idx
        para_id_for_log = paragraph.get('segment_id', f"索引 {idx}")

        # Calculate how many images are needed
        num_images_needed = calculate_num_images(paragraph)
        # Update paragraph dict in place if necessary (or just use the calculated value)
        # Let's store it in the paragraph for consistency during the run
        if paragraph.get('num_images_needed') != num_images_needed:
             paragraph['num_images_needed'] = num_images_needed
             logger.debug(f"段落 {para_id_for_log}: 计算得到 num_images_needed = {num_images_needed}")

        # Scan for existing images (local and from JSON)
        assigned_media = paragraph.get('assigned_media', {})
        existing_image_paths = _scan_existing_images(theme, idx, num_images_needed, image_dir, assigned_media)
        num_existing = len(existing_image_paths)

        # Decide if generation is required for this paragraph
        if should_generate(paragraph, num_images_needed, num_existing, flags):
            image_needs.append(ImageNeed(
                paragraph_idx=idx,
                paragraph=paragraph, # Pass the potentially updated paragraph dict
                need=num_images_needed,
                existing_paths=existing_image_paths, # Pass paths found so far
            ))
        # else: # Logged within should_generate

    logger.info(f"扫描完成: {len(image_needs)} 个段落需要处理图片生成。")
    return image_needs


def build_prompt_framework(paragraphs: list[dict], needs_map: dict[int, ImageNeed], theme: str, image_dir: str) -> list[dict]:
    """
    为所有段落构建 prompt_data_list 框架。
    对于不需要生成的段落，填充其已有的 generated_images。
    """
    prompt_data_list = []
    for idx, paragraph in enumerate(paragraphs):
        segment_id = paragraph.get('segment_id')
        # Use num_images_needed potentially updated in scan_needs
        num_images_needed = paragraph.get('num_images_needed', 1)
        assigned_media = paragraph.get('assigned_media', {})

        generated_images_for_entry = []
        if idx not in needs_map: # This paragraph is not in the list of needs to be generated
             # Rescan existing images for paragraphs not needing generation
             generated_images_for_entry = _scan_existing_images(theme, idx, num_images_needed, image_dir, assigned_media)
             logger.debug(f"段落 {segment_id or idx} (不生成): 记录现有图片 {len(generated_images_for_entry)} 张到 prompt 框架。")
        # else: # Paragraph needs generation, generated_images will be populated later in update step

        prompt_entry = {
            "segment_id": segment_id,
            "paragraph_text": paragraph.get('paragraph_text'),
            "num_images_needed": num_images_needed,
            "prompt": paragraph.get('prompt', {}), # Include initial prompt if any
            "generated_images": generated_images_for_entry, # Initially empty or pre-filled for non-generated
            "detected_characters": paragraph.get('detected_characters', None) # Include if present
        }
        prompt_data_list.append(prompt_entry)

    return prompt_data_list


# --- Prompt Generation Helper ---

def generate_prompt_map(paragraphs_missing: list[dict],
                        theme: str,
                        context_text: str,
                        generator_fn) -> dict[int, dict]:
    """
    调用 generate_prompts_for_paragraphs，返回 {paragraph_idx: prompt_dict}
    供主流程合并。
    """
    prompt_map = {}
    if not paragraphs_missing:
        logger.info("没有段落需要生成提示词。")
        return prompt_map

    logger.info(f"为 {len(paragraphs_missing)} 个段落生成提示词...")
    indices_missing = [p.get('paragraph_idx', -1) for p in paragraphs_missing]
    logger.debug(f"需要生成提示词的段落索引: {indices_missing}")

    try:
        # Assuming generator_fn takes a list of paragraphs and returns an updated list
        updated_list = generator_fn(
            paragraphs_missing, theme, context_text,
            only_process_these_paragraphs=True, # Assuming this flag is used correctly by the fn
            with_style=False # As per original call
        )
        logger.info("提示词生成完成。")

        # Create the map {paragraph_idx: prompt_dict}
        for updated_p in updated_list:
            idx = updated_p.get("paragraph_idx")
            prompt_data = updated_p.get("prompt")
            if idx is not None and isinstance(prompt_data, dict) and prompt_data: # Check if prompt is valid
                prompt_map[idx] = prompt_data
                # Optionally log detected characters if updated
                if "detected_characters" in updated_p:
                     logger.debug(f"段落索引 {idx}: 生成 prompt 并检测到角色: {updated_p['detected_characters']}")
            elif idx is not None:
                logger.warning(f"段落索引 {idx}: 生成的提示词无效或为空，无法添加到 prompt_map。")

    except Exception as e:
         logger.error(f"调用提示词生成函数或处理结果时出错: {e}", exc_info=True)
         # Should we raise? Or return partial map? Let's return empty map on error.
         return {} # Return empty map to signal failure

    logger.info(f"成功为 {len(prompt_map)} 个段落生成了有效提示词。")
    return prompt_map


# --- Task Planning and Launching ---

def prepare_prompts(paragraph: dict, num_images_needed: int) -> list[dict]:
    """
    (Copied and slightly adapted from original)
    准备用于图片生成的提示词列表。处理新旧格式，并在数量不足时复制最后一个提示词。
    """
    prompt_data = paragraph.get('prompt')
    prompt_items = []
    para_idx = paragraph.get('paragraph_idx', '未知')
    para_id_for_log = paragraph.get('segment_id', f"索引 {para_idx}")

    if not prompt_data:
        logger.warning(f"段落 {para_id_for_log} 缺少提示词数据。")
        return []

    # Handle different prompt formats
    if isinstance(prompt_data, dict) and 'prompts' in prompt_data and isinstance(prompt_data['prompts'], list):
        prompt_items = [item for item in prompt_data['prompts'] if isinstance(item, dict) and item] # Ensure items are dicts
        logger.debug(f"段落 {para_id_for_log}: 使用新格式提示词列表，找到 {len(prompt_items)} 个有效提示词。")
    elif isinstance(prompt_data, dict) and prompt_data:
        prompt_items = [prompt_data]
        logger.debug(f"段落 {para_id_for_log}: 使用旧格式单一提示词字典。")
    elif isinstance(prompt_data, str) and prompt_data:
         prompt_items = [{'flux_prompt': prompt_data}] # Assume it's the main prompt key needed
         logger.debug(f"段落 {para_id_for_log}: 使用字符串提示词，包装为字典。")
    else:
         logger.warning(f"段落 {para_id_for_log}: 提示词格式无法识别或无效: {type(prompt_data)}")
         return []

    # Duplicate last prompt if needed
    original_count = len(prompt_items)
    if 0 < original_count < num_images_needed:
        logger.debug(f"段落 {para_id_for_log}: 提示词数量不足 ({original_count}/{num_images_needed})，将复制最后一个提示词。")
        last_prompt = copy.deepcopy(prompt_items[-1])
        for _ in range(num_images_needed - original_count):
            prompt_items.append(copy.deepcopy(last_prompt))
    elif original_count == 0 and num_images_needed > 0:
         logger.error(f"段落 {para_id_for_log}: 初始有效提示词列表为空，无法生成所需的 {num_images_needed} 个提示词。")
         return []

    return prompt_items[:num_images_needed] # Return exactly the number needed


def calculate_task_plan(need: ImageNeed, flags: dict, theme: str, image_dir: str) -> list[TaskPlan]:
    """
    为单个 ImageNeed 计算需要执行的具体生成任务计划 (TaskPlan)。
    """
    task_plans = []
    para_idx = need.paragraph_idx
    paragraph = need.paragraph
    num_images_needed = need.need
    existing_paths = need.existing_paths # Paths already found during scan
    num_existing = len(existing_paths)
    should_force_generation = flags.get('should_force_generation', False)
    para_id_for_log = paragraph.get('segment_id', f"索引 {para_idx}")

    # Prepare all potential prompts for this paragraph
    all_prompts_for_para = prepare_prompts(paragraph, num_images_needed)
    if not all_prompts_for_para:
        logger.warning(f"段落 {para_id_for_log}: 无法准备有效提示词，无法制定任务计划。")
        return []

    # Determine which prompts correspond to missing images
    start_prompt_index = num_existing # 0-based index to start generating from
    max_remaining_to_generate = max(0, num_images_needed - start_prompt_index)

    # Get specific limits if any (e.g., multi_image_pending img_need_generate)
    num_to_generate = max_remaining_to_generate # Default: generate all remaining
    assigned_media = paragraph.get('assigned_media', {})
    media_type = assigned_media.get('type')
    needs_gen_flag = assigned_media.get('needs_generated_image', False)
    img_need_generate_val = assigned_media.get('img_need_generate')

    if media_type == 'multi_image_pending' and needs_gen_flag and isinstance(img_need_generate_val, int) and img_need_generate_val > 0:
        num_to_generate = min(max_remaining_to_generate, img_need_generate_val)
        logger.info(f"段落 {para_id_for_log}: multi_image_pending 限制，计划生成: {num_to_generate} (剩余空位: {max_remaining_to_generate})。")
    elif media_type == 'pending' and needs_gen_flag:
         # Original logic: generate min(remaining, 1)
         num_to_generate = min(max_remaining_to_generate, 1) if max_remaining_to_generate > 0 else 0
         logger.info(f"段落 {para_id_for_log}: pending 状态，计划生成 {num_to_generate} 张图片。")
    # Add case for 'placeholder' status='to_be_generated' - should generate all needed?
    elif media_type == 'placeholder' and assigned_media.get('status') == 'to_be_generated':
         num_to_generate = max_remaining_to_generate # Generate all needed for placeholder
         logger.info(f"段落 {para_id_for_log}: placeholder 状态，计划生成所有剩余 {num_to_generate} 张图片。")


    num_to_generate = max(0, num_to_generate) # Ensure non-negative

    if num_to_generate <= 0:
        logger.debug(f"段落 {para_id_for_log}: 计算得出无需生成新图片 (需要 {num_images_needed}, 已有 {num_existing}, 计划生成 {num_to_generate})。")
        return []

    # Select the prompts to use for generation
    end_prompt_index = start_prompt_index + num_to_generate
    # Check index validity
    if start_prompt_index >= len(all_prompts_for_para):
        logger.error(f"段落 {para_id_for_log}: 起始提示词索引 {start_prompt_index} 超出范围 ({len(all_prompts_for_para)})，无法制定计划。")
        return []

    prompts_to_process = all_prompts_for_para[start_prompt_index : min(end_prompt_index, len(all_prompts_for_para))]

    if not prompts_to_process:
        logger.warning(f"段落 {para_id_for_log}: 根据计算范围 ({start_prompt_index} to {end_prompt_index}) 未找到需要处理的提示词。")
        return []

    logger.info(f"段落 {para_id_for_log} 计划生成 {len(prompts_to_process)} 张图片 (从索引 {start_prompt_index} 开始)。")

    # Create TaskPlan objects for each prompt to process
    for i, prompt_item in enumerate(prompts_to_process):
        current_prompt_seq_0based = start_prompt_index + i
        image_sequence_index_1based = current_prompt_seq_0based + 1

        # Determine expected output path
        if num_images_needed == 1:
            image_filename = f"{theme}_{para_idx+1}.png"
        else:
            image_filename = f"{theme}_{para_idx+1}_{image_sequence_index_1based}.png"
        expected_path = str(Path(image_dir).resolve() / image_filename)

        # Final check: if not forcing, skip if file exists *just before planning*
        # This check might be slightly redundant if scan was accurate, but good safeguard
        if not should_force_generation and Path(expected_path).is_file() and Path(expected_path).stat().st_size > 0:
            logger.warning(f"段落 {para_id_for_log} 图片 {image_sequence_index_1based} 在规划时发现已存在 (非强制)，跳过此任务计划: {expected_path}")
            continue
        elif should_force_generation and Path(expected_path).is_file():
             logger.info(f"强制模式: 段落 {para_id_for_log} 图片 {image_sequence_index_1based} ({expected_path}) 已存在，仍将包含在计划中以重新生成。")

        task_plans.append(TaskPlan(
            para_idx=para_idx,
            prompt_item=prompt_item,
            expected_path=expected_path,
            prompt_seq=current_prompt_seq_0based,
            image_sequence_index_1based=image_sequence_index_1based
        ))

    return task_plans


def launch_tasks(plans: list[TaskPlan], workflow_data: dict, workflow_cfg: dict, server: str) -> list[TaskInfo]:
    """
    根据任务计划列表，向 ComfyUI 提交生成任务。
    """
    submitted_tasks = []
    total_plans = len(plans)
    logger.info(f"准备根据 {total_plans} 个任务计划提交生成请求...")

    for i, plan in enumerate(plans):
        para_idx = plan.para_idx
        para_id_for_log = f"索引 {para_idx}" # TODO: Get segment_id if available easily? Plan doesn't pass paragraph here.

        logger.info(f"处理计划 {i+1}/{total_plans}: 段落 {para_id_for_log} 图片 {plan.image_sequence_index_1based} (prompt seq {plan.prompt_seq})")

        try:
            # Prepare workflow for this specific task
            # Use deepcopy to avoid modifying the template for subsequent tasks
            modified_workflow = comfyui_client.set_workflow_parameters(
                copy.deepcopy(workflow_data),
                plan.prompt_item,
                workflow_cfg
            )

            # Submit to ComfyUI queue
            logger.debug(f"提交任务到 ComfyUI: 段落 {para_id_for_log} 图片 {plan.image_sequence_index_1based}")
            prompt_id = comfyui_client.queue_prompt(server, modified_workflow)

            if prompt_id:
                submitted_tasks.append(TaskInfo(
                    para_idx=para_idx,
                    prompt_id=prompt_id,
                    expected_path=plan.expected_path,
                    prompt_seq=plan.prompt_seq
                ))
                logger.info(f"任务提交成功: 段落 {para_id_for_log} 图片 {plan.image_sequence_index_1based} -> ID: {prompt_id}")
            else:
                # queue_prompt likely raises on connection error, None might mean other failure
                logger.error(f"任务提交失败 (未收到 prompt_id): 段落 {para_id_for_log} 图片 {plan.image_sequence_index_1based}")

        except ConnectionError as e:
             logger.error(f"连接 ComfyUI 失败 ({server}): {e}，无法提交任务 (段落 {para_id_for_log} 图片 {plan.image_sequence_index_1based})")
             # Optionally break or continue? Let's continue trying others.
        except Exception as e:
            logger.error(f"设置或提交任务时意外出错: 段落 {para_id_for_log} 图片 {plan.image_sequence_index_1based} - {e}", exc_info=True)

    logger.info(f"总共成功提交了 {len(submitted_tasks)} 个图片生成任务。")
    return submitted_tasks


# --- Result Collection (Sequential) ---

# collect_and_download remains largely the same as it was already sequential
# Renaming parameter 'workflow_config' to 'workflow_cfg' for consistency if needed
def collect_and_download(
    tasks: list[TaskInfo],
    server_address: str,
    workflow_cfg: dict, # Use consistent name
    timeout: int
) -> dict[int, dict[int, str]]:
    """
    ( Largely unchanged )
    轮询已提交任务的状态，下载生成的图片 (顺序执行)。

    Args:
        tasks: 已提交的任务信息列表 (TaskInfo)。
        server_address: ComfyUI 服务器地址。
        workflow_cfg: 工作流配置，用于 check_queue_status。
        timeout: 单个任务的最长等待时间。

    Returns:
        dict: {para_idx: {prompt_seq: abs_path}} 结构，包含成功下载的图片路径。
    """
    results = {} # {para_idx: {prompt_seq: abs_path}}
    successful_tasks = 0
    failed_tasks = 0
    total_tasks = len(tasks)
    logger.info(f"开始收集 {total_tasks} 个已提交任务的结果...")

    for i, task in enumerate(tasks):
        para_idx = task.para_idx
        prompt_id = task.prompt_id
        expected_path = task.expected_path
        prompt_seq = task.prompt_seq
        image_sequence_1based = prompt_seq + 1
        para_id_for_log = f"索引 {para_idx}" # TODO: get segment_id if possible?

        logger.info(f"查询任务 {i+1}/{total_tasks} (段落 {para_id_for_log} 图片 {image_sequence_1based}, ID: {prompt_id})...")

        try:
            # Check status and get image info from ComfyUI
            image_info = comfyui_client.check_queue_status(
                server_address,
                prompt_id,
                workflow_cfg, # Pass the config
                timeout=timeout
            )

            if image_info:
                logger.info(f"任务 {prompt_id} 成功，准备下载图片到: {expected_path}")
                # Download the image
                downloaded_path = comfyui_client.download_image(
                    server_address,
                    image_info,
                    expected_path, # Download directly to the target path
                    max_retries=3
                )

                if downloaded_path:
                    final_image_path = str(Path(downloaded_path).resolve())
                    # Store result: {para_idx: {prompt_seq: path}}
                    if para_idx not in results:
                        results[para_idx] = {}
                    results[para_idx][prompt_seq] = final_image_path
                    successful_tasks += 1
                    logger.info(f"段落 {para_id_for_log} 的第 {image_sequence_1based} 个图片下载成功: {final_image_path}")
                else:
                    failed_tasks += 1
                    logger.error(f"段落 {para_id_for_log} 的第 {image_sequence_1based} 个图片下载失败 (download_image 返回 None)。")
            else:
                # check_queue_status handles logging for failure/timeout
                failed_tasks += 1
                logger.error(f"段落 {para_id_for_log} 的第 {image_sequence_1based} 个图片生成失败或未找到输出 (check_queue_status 返回 None)。")

        except TimeoutError as e:
             failed_tasks += 1
             logger.error(f"查询段落 {para_id_for_log} 的第 {image_sequence_1based} 个任务 ({prompt_id}) 超时: {e}")
        except ConnectionError as e:
             failed_tasks += 1
             logger.error(f"连接 ComfyUI 失败 ({server_address}): {e}，无法查询任务 {prompt_id}")
             # Maybe add a longer sleep or break if connection fails repeatedly?
        except Exception as e:
            failed_tasks += 1
            logger.error(f"处理段落 {para_id_for_log} 的第 {image_sequence_1based} 个任务 ({prompt_id}) 时意外出错: {str(e)}", exc_info=True)

    logger.info(f"结果收集完成: {successful_tasks} 个任务成功下载图片，{failed_tasks} 个任务失败。")
    return results


# --- Update Logic Helpers ---

def merge_image_paths(existing_paths: list[str], generated_results: dict[int, str]) -> list[str]:
    """
    合并扫描时已有的图片路径和新生成的图片路径。

    Args:
        existing_paths: scan_needs 时找到的路径列表。
        generated_results: {prompt_seq: abs_path} 字典，包含新下载的图片。

    Returns:
        list[str]: 合并、去重、排序后的最终图片路径列表。
    """
    # Extract new paths, already sorted by prompt_seq in collect_and_download if processed sequentially
    new_paths_sorted_by_seq = [path for seq, path in sorted(generated_results.items())]

    # Combine, ensuring uniqueness (prefer existing if duplicates somehow occur)
    # Use dict.fromkeys for ordered unique set
    combined_paths_dict = dict.fromkeys(existing_paths)
    combined_paths_dict.update(dict.fromkeys(new_paths_sorted_by_seq)) # Add new ones
    final_paths = list(combined_paths_dict.keys())

    # Re-sort based on filename convention as a final guarantee
    def sort_key(p):
        match = re.findall(r'_(\d+)(?:_(\d+))?\.png$', Path(p).name)
        if match:
            g1 = int(match[0][0]) if match[0][0] else 0
            g2 = int(match[0][1]) if match[0][1] else 1 # Treat single as _N_1
            return (g1, g2)
        else:
            return (float('inf'), float('inf'))

    final_paths.sort(key=sort_key)
    return final_paths

# update_assigned_media is kept as is, potentially renamed if desired
# Let's keep the name for now as it's descriptive
def update_assigned_media(paragraph: dict, final_image_paths: list[str], num_images_needed: int):
    """( Original Function - Kept ) 根据最终的图片列表更新段落的 assigned_media 字段"""
    num_images_final = len(final_image_paths)
    para_idx = paragraph.get('paragraph_idx', '未知')
    para_id_for_log = paragraph.get('segment_id', f"索引 {para_idx}")

    logger.debug(f"更新段落 {para_id_for_log} 的 assigned_media: 需要 {num_images_needed}, 最终获得 {num_images_final} 张图片。")

    assigned_media = paragraph.get('assigned_media', {}) # Get current state

    if num_images_final >= num_images_needed and num_images_needed == 1:
        # Single image needed, single image generated/found
        assigned_media = {
            'type': 'image',
            'filepath': final_image_paths[0], # Use the first (and only) path
            'duration': None, 'start_time': None, 'end_time': None, 'similarity': 1.0
        }
        logger.info(f"段落 {para_id_for_log}: 更新为 'image' 状态。")
    elif num_images_final >= num_images_needed and num_images_needed > 1:
        # Multi-image needed, sufficient images generated/found
        assigned_media = {
            'type': 'multi_image',
            'filepaths': sorted(final_image_paths)[:num_images_needed], # Take only needed count, ensure sorted
            'duration': None, 'start_time': None, 'end_time': None, 'similarity': 1.0
        }
        logger.info(f"段落 {para_id_for_log}: 更新为 'multi_image' 状态 (使用 {num_images_needed} 张图片)。")
    elif 0 < num_images_final < num_images_needed:
        # Partially completed (got some, but not all needed)
        status_msg = f"Generation partially completed. Got {num_images_final}/{num_images_needed} images."
        assigned_media = {
            'type': 'multi_image_pending', # Stay in pending state
            'needs_generated_image': True, # Still needs more
            'num_images_needed': num_images_needed,
            'filepaths': sorted(final_image_paths), # Keep paths to those generated
            'status_message': status_msg
            # Keep other fields? Or reset them? Let's keep it simple for now.
        }
        logger.warning(f"段落 {para_id_for_log}: 图片不足，更新为 'multi_image_pending' 状态 ({status_msg})。")
    elif num_images_final == 0 and num_images_needed >= 1:
        # Failed to get any images when at least one was needed
        original_type = assigned_media.get('type')
        # Only update if it wasn't already a final non-generated type
        if original_type not in ['clip', 'anchor']: # maybe also exclude 'image', 'multi_image'?
            if num_images_needed == 1:
                assigned_media = {
                    'type': 'placeholder', # Revert to placeholder or keep original pending type? Let's use placeholder.
                    'status': 'generation_failed',
                    'needs_generated_image': True, # Mark for retry
                    'num_images_needed': num_images_needed,
                    'filepaths': [],
                    'status_message': f"Failed to generate the required image."
                }
                logger.error(f"段落 {para_id_for_log}: 未能生成所需的 1 张图片，状态设为 placeholder/generation_failed。")
            else: # num_images_needed > 1
                 assigned_media = {
                    'type': 'multi_image_pending', # Keep pending type
                    'needs_generated_image': True,
                    'num_images_needed': num_images_needed,
                    'filepaths': [],
                    'status_message': f"Failed to generate any of the {num_images_needed} required images."
                 }
                 logger.error(f"段落 {para_id_for_log}: 未能生成任何所需的 {num_images_needed} 张图片，状态设为 multi_image_pending。")
        else:
            logger.warning(f"段落 {para_id_for_log}: 未生成图片，但原始类型为 {original_type}，保留原 assigned_media。")
    else: # num_images_final >= num_images_needed == 0 (shouldn't happen) or other odd cases
        logger.warning(f"段落 {para_id_for_log}: 更新 assigned_media 时遇到未覆盖的情况 (需要 {num_images_needed}, 获得 {num_images_final})。保留原始 assigned_media: {assigned_media}")
        # Keep original assigned_media in unexpected cases

    # Update the paragraph dictionary
    paragraph['assigned_media'] = assigned_media


# New helper according to plan
def apply_media_update(paragraph: dict, merged_paths: list[str], num_needed: int):
    """Thin wrapper to call the (potentially renamed) update_assigned_media."""
    update_assigned_media(paragraph, merged_paths, num_needed)

# New helper according to plan
def sync_prompt_entry(prompt_entry: dict, paragraph: dict, merged_paths: list[str]):
    """
    Updates a single entry in the prompt_data_list with the final state.
    """
    if not prompt_entry or not isinstance(prompt_entry, dict):
        logger.warning("sync_prompt_entry called with invalid prompt_entry.")
        return

    para_idx = paragraph.get('paragraph_idx', '未知')
    para_id_for_log = paragraph.get('segment_id', f"索引 {para_idx}")

    # Update generated_images with the final list
    prompt_entry['generated_images'] = merged_paths
    # Update prompt from the paragraph (in case it was generated/modified)
    prompt_entry['prompt'] = paragraph.get('prompt', {})
    # Update detected characters if present in paragraph
    if 'detected_characters' in paragraph:
        prompt_entry['detected_characters'] = paragraph.get('detected_characters')
    # Update num_images_needed from paragraph
    prompt_entry['num_images_needed'] = paragraph.get('num_images_needed', 1)

    logger.debug(f"Prompt 条目 (段落 {para_id_for_log}) 已同步: {len(merged_paths)} 张图片。")


# New helper according to plan
def cleanup_paragraph(paragraph: dict):
    """Removes temporary or intermediate fields from the paragraph dictionary."""
    # Remove fields used during generation but not needed in final output json
    # Keep 'assigned_media', 'paragraph_text', 'segment_id', 'num_images_needed' (if needed downstream)
    # Remove 'prompt', 'generated_image(s)' (old fields), 'detected_characters' (moved to prompt json)
    # Remove 'paragraph_idx' added internally
    keys_to_remove = ['prompt', 'generated_image', 'generated_images', 'detected_characters', 'paragraph_idx']
    for key in keys_to_remove:
        paragraph.pop(key, None)
    # logger.debug(f"清理段落 {paragraph.get('segment_id', '')} 字段。")


# --- Update Orchestrator ---

def update_all_paragraphs(
    paragraphs: list[dict],
    generation_results: dict[int, dict[int, str]], # {para_idx: {prompt_seq: path}}
    image_needs: list[ImageNeed],
    prompt_data_list: list[dict]
):
    """
    Orchestrates the update process for all paragraphs after generation attempts.
    Updates paragraphs that were processed and cleans up all paragraphs.
    Updates the prompt_data_list accordingly.
    """
    needs_map = {need.paragraph_idx: need for need in image_needs}

    # Update paragraphs that were involved in generation
    for need in image_needs:
        idx = need.paragraph_idx
        paragraph = need.paragraph # This is the dict from the main list
        num_needed = need.need
        initial_paths = need.existing_paths
        newly_generated_map = generation_results.get(idx, {})

        # 1. Merge existing and new paths
        merged_paths = merge_image_paths(initial_paths, newly_generated_map)

        # 2. Update the paragraph's assigned_media based on merged paths
        apply_media_update(paragraph, merged_paths, num_needed)

        # 3. Sync the corresponding entry in prompt_data_list
        if idx < len(prompt_data_list):
            sync_prompt_entry(prompt_data_list[idx], paragraph, merged_paths)
        else:
            para_id_for_log = paragraph.get('segment_id', f"索引 {idx}")
            logger.error(f"索引 {idx} 超出 prompt_data_list 范围 ({len(prompt_data_list)})，无法同步 prompt 条目 (段落 {para_id_for_log})。")

    # Cleanup fields for ALL paragraphs (processed or not)
    for idx, paragraph in enumerate(paragraphs):
        # Special handling for paragraphs that were *not* processed but might need prompt_list sync
        if idx not in needs_map:
            # These paragraphs should already have their final assigned_media
            # We just need to ensure their prompt_data_list entry is correct
            if idx < len(prompt_data_list):
                 # Assume prompt_data_list[idx]['generated_images'] was set correctly in build_prompt_framework
                 # We might need to re-sync prompt and detected_chars if they could change outside generation
                 prompt_entry = prompt_data_list[idx]
                 prompt_entry['prompt'] = paragraph.get('prompt', {}) # Sync prompt just in case
                 if 'detected_characters' in paragraph: # Sync chars
                    prompt_entry['detected_characters'] = paragraph.get('detected_characters')
                 # generated_images should be correct from build_prompt_framework
                 logger.debug(f"同步未处理段落 {idx} 的 prompt/chars 到 prompt 条目。")
            else:
                 para_id_for_log = paragraph.get('segment_id', f"索引 {idx}")
                 logger.error(f"索引 {idx} 超出 prompt_data_list 范围，无法同步未处理的段落 {para_id_for_log}。")

        # Apply cleanup to the paragraph dict itself
        cleanup_paragraph(paragraph)

    logger.info("所有段落状态更新和清理完成。")


# --- JSON Saving ---

# save_prompt_json remains largely the same
def save_prompt_json(prompt_data_list: list[dict], base_json_path_str: Optional[str], theme: str):
    """( Largely unchanged ) 保存 prompt 数据到 _prompt.json 文件"""
    if not prompt_data_list:
        logger.info("没有 prompt 数据需要保存。")
        return

    prompt_output_path = None
    if base_json_path_str:
         base_path = Path(base_json_path_str)
         # Try to determine output dir and name
         output_dir = base_path.parent
         # Handle different input suffixes
         name_without_suffix = base_path.stem
         if name_without_suffix.endswith('_result'):
             name_without_suffix = name_without_suffix[:-len('_result')]
         elif name_without_suffix.endswith('_audio'):
             name_without_suffix = name_without_suffix[:-len('_audio')]
         elif name_without_suffix.endswith('_final'):
              name_without_suffix = name_without_suffix[:-len('_final')]
         # Construct prompt filename using theme if available, else base name
         prompt_filename = f"{theme or name_without_suffix}_prompt.json"
         prompt_output_path = output_dir / prompt_filename

    else:
         # Fallback: save in current working directory
         logger.warning("无法确定基础 JSON 路径，prompt 文件将保存在当前工作目录。")
         prompt_output_path = Path(f"{theme}_prompt.json") # Use theme name

    prompt_output_path_str = str(prompt_output_path)

    try:
        with open(prompt_output_path_str, 'w', encoding='utf-8') as f:
            json.dump(prompt_data_list, f, ensure_ascii=False, indent=2)
        logger.info(f"Prompt 数据已保存到: {prompt_output_path_str}")
    except Exception as e:
        logger.error(f"保存 prompt 数据到 {prompt_output_path_str} 时出错: {e}", exc_info=True)


# ===== New Main Business Logic Function =====

def process_json_file(json_path: str, image_dir: str, theme: Optional[str] = None, force: bool = False):
    """
    处理输入的 JSON 文件，执行图片生成逻辑，并保存结果。
    这是核心业务逻辑，不含 CLI 解析。

    Args:
        json_path: 输入 JSON 文件路径。
        image_dir: 图片输出目录。
        theme: 主题名称 (可选, 会尝试从文件名推断)。
        force: 是否强制重新生成。

    Returns:
        list: 更新后的段落列表 (用于可能的后续处理，或 None on error)。
    """
    try:
        # Determine theme if not provided
        if not theme:
            base_name = Path(json_path).stem
            # More robust theme extraction
            match = re.match(r"(.+?)(_result|_audio|_prompt|_final|_gen_image|_assign_image)?$", base_name)
            if match:
                theme = match.group(1)
                logger.info(f"从文件名 '{Path(json_path).name}' 提取主题: {theme}")
            else:
                theme = base_name # Fallback
                logger.warning(f"无法从文件名 '{Path(json_path).name}' 标准提取主题，使用基础名称: {theme}")

        # Read input JSON
        logger.info(f"读取输入文件: {json_path}")
        with open(json_path, 'r', encoding='utf-8') as f:
            try:
                paragraphs = json.load(f)
                if not isinstance(paragraphs, list):
                     logger.error(f"JSON 文件顶层不是列表: {json_path}")
                     return None # Indicate error
            except json.JSONDecodeError as e:
                logger.error(f"解析 JSON 文件失败: {json_path} - {e}")
                return None # Indicate error

        # Ensure image directory exists
        os.makedirs(image_dir, exist_ok=True)
        logger.info(f"图片输出目录: {Path(image_dir).resolve()}")

        # Call the main generation logic function
        logger.info(f"开始为主题 '{theme}' 生成图片 (强制: {force})...")
        # Pass base_json_path for saving prompt json correctly
        modified_paragraphs = generate_images_main(
            paragraphs,
            image_dir,
            theme,
            force_regenerate=force,
            base_json_path=json_path
        )

        # Save the updated main JSON data (without temp fields)
        # Determine output path based on input path convention
        in_path = Path(json_path)
        out_filename = f"{theme}_final.json" # Consistent output name
        output_path = str(in_path.parent / out_filename)

        # Check if output path is same as input, add suffix if needed (shouldn't happen with _final.json)
        if output_path == json_path:
             output_path = str(in_path.with_suffix('.final.json'))
             logger.warning(f"输出路径与输入路径相同，将使用: {output_path}")

        logger.info(f"准备保存最终结果到: {output_path}")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(modified_paragraphs, f, ensure_ascii=False, indent=2)
        logger.info(f"图片生成流程完成，主结果已保存至 {output_path}")
        # Prompt data saving is handled inside generate_images_main

        return modified_paragraphs

    except FileNotFoundError as e:
         logger.error(f"处理文件时未找到: {e}")
         return None
    except Exception as e:
        logger.error(f"处理 JSON 文件 {json_path} 时发生未预期错误: {e}", exc_info=True)
        # Don't re-raise here, return None to signal failure to CLI
        return None


# ===== Refactored Main Generation Orchestrator =====

def generate_images_main(paragraphs: list, image_dir: str, theme: str, force_regenerate: bool = False, base_json_path: Optional[str] = None):
    """
    ( Refactored Version )
    为段落列表生成图片。协调扫描、提示词填充、任务提交、结果收集和段落更新。
    """
    # Combine force flag and global debug flag
    image_generate_debug = IMAGE_GENERATE_DEBUG # Get current global state
    should_force_generation = force_regenerate or image_generate_debug

    if image_generate_debug: logger.info("图片生成调试模式已启用。")
    if force_regenerate: logger.info("强制重新生成模式已启用。")
    if should_force_generation: logger.info("将强制生成已存在的所需图片。")

    flags = {"should_force_generation": should_force_generation}

    # Ensure image directory exists
    os.makedirs(image_dir, exist_ok=True)

    # --- Get ComfyUI Config (Server, Workflow, Timeout) ---
    server_address = get_param('IMAGE_GEN.SERVER_ADDRESS', "127.0.0.1:8188")
    default_workflow_path = "assets/workflow/storytelling-api.json" # Default relative to proj root?
    workflow_path_param = get_param('IMAGE_GEN.WORKFLOW_PATH', default_workflow_path)
    comfyui_consts = comfyui_client.get_comfyui_constants()
    queue_timeout = comfyui_consts.get("QUEUE_TIMEOUT", 1800) # Timeout for waiting

    # Find and load workflow
    workflow_path = None
    # Define search paths more clearly: 1. Param, 2. Relative to this script, 3. Relative to CWD?
    script_dir = Path(__file__).parent
    possible_paths = [
        Path(workflow_path_param),
        script_dir / ".." / default_workflow_path, # Assumes script is in a subdir
        Path.cwd() / default_workflow_path
    ]
    for p in possible_paths:
        try:
             abs_p = p.resolve()
             if abs_p.is_file():
                 workflow_path = str(abs_p)
                 logger.info(f"找到工作流文件: {workflow_path}")
                 break
        except Exception: # Handle potential resolution errors
             pass

    if not workflow_path:
        logger.error(f"错误：工作流文件在所有检查路径均未找到: {[str(p) for p in possible_paths]}")
        raise FileNotFoundError(f"工作流文件未找到: {workflow_path_param} 或其备选路径")

    logger.info(f"使用ComfyUI服务器地址: {server_address}")
    logger.info(f"使用工作流文件: {workflow_path}")

    try:
        workflow_data = comfyui_client.load_workflow(workflow_path)
        workflow_config = comfyui_client.get_workflow_config_by_path(workflow_path)
        if not workflow_config:
             raise ValueError(f"无法获取工作流 '{workflow_path}' 的有效配置。")
    except Exception as e:
         logger.error(f"加载或配置工作流 '{workflow_path}' 失败: {e}", exc_info=True)
         raise # Reraise to stop execution

    # ===== Main Generation Flow =====
    try:
        # 1. Scan paragraphs to identify needs
        image_needs = scan_needs(paragraphs, theme, image_dir, flags)
        needs_map = {need.paragraph_idx: need for need in image_needs}

        # 2. Build the framework for the prompt data list (for all paragraphs)
        prompt_data_list = build_prompt_framework(paragraphs, needs_map, theme, image_dir)

        generation_results = {} # To store {para_idx: {prompt_seq: path}}

        if not image_needs:
            logger.info("根据扫描结果，没有段落需要生成新图片。")
        else:
            logger.info(f"共发现 {len(image_needs)} 个段落需要处理图片生成。")

            # 3. Generate missing prompts
            paragraphs_needing_prompts = [need.paragraph for need in image_needs if not need.paragraph.get('prompt')]
            indices_needing_prompts = [p['paragraph_idx'] for p in paragraphs_needing_prompts]

            if paragraphs_needing_prompts:
                full_text = "\n".join([p.get('paragraph_text', '') for p in paragraphs if p.get('paragraph_text')])
                # Call the prompt generation function, get a map back
                generated_prompt_map = generate_prompt_map(
                    paragraphs_needing_prompts,
                    theme,
                    full_text,
                    generate_prompts_for_paragraphs # Pass the actual function
                )
                # Merge generated prompts back into the main paragraphs list and needs list
                for idx, prompt_dict in generated_prompt_map.items():
                    if 0 <= idx < len(paragraphs):
                         paragraphs[idx]['prompt'] = prompt_dict
                         # Also update the paragraph dict within the corresponding ImageNeed object
                         if idx in needs_map:
                              needs_map[idx].paragraph['prompt'] = prompt_dict
                         # No need to update prompt_data_list here, sync_prompt_entry handles it later
                         logger.debug(f"段落索引 {idx}: 已合并生成的 prompt。")
                    else:
                         logger.warning(f"生成的 Prompt 映射包含无效索引 {idx}，无法合并。")
            else:
                logger.info("所有需要处理的段落均已有提示词。")


            # 4. Calculate task plans and launch tasks
            all_task_plans = []
            for need in image_needs:
                # Ensure the paragraph in 'need' has the latest prompt if generated
                # (It should if updated via needs_map above)
                paragraph_with_prompt = need.paragraph # paragraphs[need.paragraph_idx]
                if not paragraph_with_prompt.get('prompt'):
                     para_id_for_log = paragraph_with_prompt.get('segment_id', f"索引 {need.paragraph_idx}")
                     logger.warning(f"段落 {para_id_for_log} 在规划任务前仍缺少提示词，跳过此段落。")
                     continue

                plans_for_need = calculate_task_plan(need, flags, theme, image_dir)
                all_task_plans.extend(plans_for_need)

            if not all_task_plans:
                 logger.info("没有计算出需要执行的任务计划。")
            else:
                 # Launch tasks based on the generated plans
                 submitted_tasks = launch_tasks(all_task_plans, workflow_data, workflow_config, server_address)

                 # 5. Collect results (sequentially)
                 if submitted_tasks:
                     generation_results = collect_and_download(submitted_tasks, server_address, workflow_config, timeout=queue_timeout)
                     logger.info(f"图片生成和下载完成，获得 {len(generation_results)} 个段落的结果 ({sum(len(v) for v in generation_results.values())} 张图片)。")
                 else:
                     logger.info("没有任务被成功提交或需要执行。")

        # 6. Update all paragraphs and the prompt data list with results
        update_all_paragraphs(paragraphs, generation_results, image_needs, prompt_data_list)

        # 7. Save the final prompt data list
        save_prompt_json(prompt_data_list, base_json_path, theme)

    except FileNotFoundError as e:
         logger.error(f"文件未找到错误: {e}", exc_info=True)
         raise
    except ConnectionError as e:
         logger.error(f"ComfyUI 连接错误: {e}", exc_info=True)
         raise # Stop execution on connection error during main flow
    except Exception as e:
        logger.error(f"图片生成主流程中发生未预期的错误: {e}", exc_info=True)
        raise # Reraise unexpected errors

    logger.info("generate_images_main 执行完毕。")
    # Paragraphs list has been modified in place, including cleanup
    return paragraphs


# ===== Old Functions (Can be removed after verification) =====

# def _calculate_real_num_images_needed(paragraph: dict) -> int: ... # Replaced by calculate_num_images
# def _should_process_paragraph(...) -> bool: ... # Replaced by should_generate + helpers
# def scan_paragraphs(...) -> tuple[list[ImageNeed], list[dict]]: ... # Replaced by scan_needs & build_prompt_framework
# def fill_missing_prompts(...): ... # Logic moved into generate_images_main using generate_prompt_map
# def submit_generation_tasks(...): ... # Replaced by calculate_task_plan & launch_tasks
# def update_paragraphs_with_results(...): ... # Replaced by update_all_paragraphs orchestrator

# Keep process_placeholder_json temporarily as it might be called externally?
# Refactor it to just call the new process_json_file
def process_placeholder_json(json_path: str, image_dir: str, theme: str = None, force: bool = False):
    """
    [Deprecated Wrapper] Calls the new process_json_file function.
    Retained for backward compatibility if called directly elsewhere.
    """
    logger.warning("调用了已弃用的 process_placeholder_json，请更新调用为 process_json_file。")
    return process_json_file(json_path, image_dir, theme, force)


# ===== Command Line Interface =====

def _cli_main():
    """Parses command line arguments and runs the image generation process."""
    parser = argparse.ArgumentParser(description="为文本段落生成图片")
    parser.add_argument("--json", type=str, required=True, help="输入JSON文件路径 (例如 _result.json 或 _audio.json)")
    parser.add_argument("--image_dir", type=str, required=True, help="图片输出目录")
    parser.add_argument("--theme", type=str, help="主题名称（可选，会尝试从输入json文件名推断）")
    parser.add_argument("--force", action='store_true', help="强制重新生成已有图片")
    # Removed --using_anchor as it was deprecated
    # Removed --process_placeholders as it's default now
    parser.add_argument("--debug", action='store_true', help="启用调试模式 (覆盖全局 IMAGE_GENERATE_DEBUG)")
    args = parser.parse_args()

    # Set global debug flag based on CLI arg
    global IMAGE_GENERATE_DEBUG
    if args.debug:
       IMAGE_GENERATE_DEBUG = True
       logger.setLevel(logging.DEBUG) # Enable more detailed logging
       for handler in logger.handlers: # Ensure handlers also respect the level
            handler.setLevel(logging.DEBUG)
       logger.info("调试模式已通过命令行启用")
    else:
       # Respect existing logger level if not debugging via CLI
       pass

    # Call the main business logic function
    result = process_json_file(args.json, args.image_dir, args.theme, args.force)

    if result is None:
        logger.error("图片生成过程失败。")
        sys.exit(1)
    else:
        logger.info("图片生成过程成功完成。")
        sys.exit(0)

if __name__ == "__main__":
    _cli_main() # Call the CLI entry point

