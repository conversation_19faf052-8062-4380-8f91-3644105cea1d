#!/usr/bin/env python3
"""
Test script to verify that Round priority fixes work correctly.
Tests that clips assigned in Round 2 are not overwritten by Round 3/4.
"""

import subprocess
import sys
import os
import json
from analyze_assignment_results import analyze_assignment_results

def test_round_priority_protection():
    """Test that Round 2 clip assignments are protected from Round 3/4 overwrites."""
    
    print("=== Testing Round Priority Protection ===")
    
    # Backup the original results
    original_file = "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_images.json"
    backup_file = original_file + ".priority_test_backup"
    
    if os.path.exists(original_file):
        print(f"Backing up original results to {backup_file}")
        subprocess.run(["cp", original_file, backup_file], check=True)
    
    # Run assignment with the fixed priority logic
    cmd = [
        "python3", "assign_clip_image.py",
        "--json", "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_script.json",
        "--image_dir", "1-theme-talker/results/Putten_murder_case_Netherlands/images",
        "--clips_json", "1-theme-talker/results/Putten_murder_case_Netherlands/assigned_clips.json",
        "--theme", "Putten_murder_case_Netherlands",
        "--reranker-threshold", "0.10"
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode != 0:
            print(f"Assignment failed with return code {result.returncode}")
            print("STDOUT:", result.stdout[-1000:])
            print("STDERR:", result.stderr[-1000:])
            return False
        
        print("Assignment completed successfully!")
        
        # Analyze the results to check for priority protection
        if os.path.exists(original_file):
            print("\n=== Analyzing Priority Protection Results ===")
            
            with open(original_file, 'r') as f:
                data = json.load(f)
            
            # Count different assignment types
            clip_assignments = 0
            image_assignments = 0
            multi_image_assignments = 0
            fallback_assignments = 0
            pending_assignments = 0
            
            # Track which segments should have clips vs images
            clips_json_path = "1-theme-talker/results/Putten_murder_case_Netherlands/assigned_clips.json"
            expected_clips = set()
            
            if os.path.exists(clips_json_path):
                with open(clips_json_path, 'r') as f:
                    clips_data = json.load(f)
                    for clip_segment in clips_data:
                        if clip_segment.get('assigned_clips'):
                            expected_clips.add(str(clip_segment['segment_id']))
            
            # Analyze actual assignments
            priority_violations = []
            
            for segment in data:
                segment_id = str(segment['segment_id'])
                media = segment.get('assigned_media', {})
                media_type = media.get('type')
                
                if media_type == 'clip':
                    clip_assignments += 1
                elif media_type == 'image':
                    image_assignments += 1
                    if media.get('fallback_assignment'):
                        fallback_assignments += 1
                elif media_type == 'multi_image':
                    multi_image_assignments += 1
                elif media_type == 'pending':
                    pending_assignments += 1
                
                # Check for priority violations
                if segment_id in expected_clips and media_type != 'clip':
                    priority_violations.append({
                        'segment_id': segment_id,
                        'expected': 'clip',
                        'actual': media_type,
                        'fallback': media.get('fallback_assignment', False)
                    })
            
            print(f"Assignment Summary:")
            print(f"  Clip assignments: {clip_assignments}")
            print(f"  Image assignments: {image_assignments}")
            print(f"  Multi-image assignments: {multi_image_assignments}")
            print(f"  Fallback assignments: {fallback_assignments}")
            print(f"  Pending assignments: {pending_assignments}")
            print(f"  Expected clips: {len(expected_clips)}")
            print(f"  Priority violations: {len(priority_violations)}")
            
            # Check for success
            if len(priority_violations) == 0:
                print("✅ PRIORITY PROTECTION WORKING!")
                print("All expected clip assignments are preserved")
                return True
            else:
                print("❌ PRIORITY VIOLATIONS DETECTED!")
                print("The following segments should have clips but have other assignments:")
                for violation in priority_violations[:5]:  # Show first 5
                    print(f"  Segment {violation['segment_id']}: expected {violation['expected']}, got {violation['actual']}")
                if len(priority_violations) > 5:
                    print(f"  ... and {len(priority_violations) - 5} more violations")
                return False
        else:
            print("❌ Results file not found after assignment")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Assignment timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running assignment: {e}")
        return False

def analyze_segment_id_mapping():
    """Analyze segment_id mapping to ensure consistency."""
    
    print("\n=== Analyzing Segment ID Mapping ===")
    
    script_file = "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_script.json"
    results_file = "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_images.json"
    
    if not os.path.exists(script_file) or not os.path.exists(results_file):
        print("❌ Required files not found for segment ID analysis")
        return False
    
    try:
        # Load both files
        with open(script_file, 'r') as f:
            script_data = json.load(f)
        
        with open(results_file, 'r') as f:
            results_data = json.load(f)
        
        # Check segment_id consistency
        script_segments = {item['segment_id'] for item in script_data}
        result_segments = {item['segment_id'] for item in results_data}
        
        print(f"Script segments: {len(script_segments)} (range: {min(script_segments)} - {max(script_segments)})")
        print(f"Result segments: {len(result_segments)} (range: {min(result_segments)} - {max(result_segments)})")
        
        # Check for mismatches
        missing_in_results = script_segments - result_segments
        extra_in_results = result_segments - script_segments
        
        if missing_in_results:
            print(f"❌ Missing in results: {sorted(list(missing_in_results))[:10]}")
        
        if extra_in_results:
            print(f"❌ Extra in results: {sorted(list(extra_in_results))[:10]}")
        
        if not missing_in_results and not extra_in_results:
            print("✅ SEGMENT ID MAPPING CONSISTENT!")
            return True
        else:
            print("❌ Segment ID mapping has issues")
            return False
            
    except Exception as e:
        print(f"❌ Error analyzing segment IDs: {e}")
        return False

def restore_original():
    """Restore the original results."""
    original_file = "1-theme-talker/results/Putten_murder_case_Netherlands/Putten_murder_case_Netherlands_images.json"
    backup_file = original_file + ".priority_test_backup"
    
    if os.path.exists(backup_file):
        print(f"Restoring original results from {backup_file}")
        subprocess.run(["cp", backup_file, original_file], check=True)
        print("Original results restored")
    else:
        print("No backup file found to restore")

if __name__ == "__main__":
    print("🚀 Testing Round Priority Protection Fixes")
    print("This will verify that Round 2 clip assignments are not overwritten by Round 3/4")
    
    # Test 1: Priority protection
    priority_success = test_round_priority_protection()
    
    # Test 2: Segment ID mapping
    mapping_success = analyze_segment_id_mapping()
    
    # Summary
    print("\n" + "="*60)
    print("PRIORITY FIX TEST SUMMARY")
    print("="*60)
    
    if priority_success:
        print("✅ Round Priority Protection: WORKING")
        print("   - Clip assignments are preserved")
        print("   - No priority violations detected")
    else:
        print("❌ Round Priority Protection: NEEDS IMPROVEMENT")
    
    if mapping_success:
        print("✅ Segment ID Mapping: CONSISTENT")
        print("   - All segment IDs match between script and results")
    else:
        print("❌ Segment ID Mapping: HAS ISSUES")
    
    if priority_success and mapping_success:
        print("\n🎉 ALL PRIORITY FIXES WORKING CORRECTLY!")
        print("The system now properly:")
        print("1. Preserves Round 2 clip assignments")
        print("2. Maintains consistent segment ID mapping")
        print("3. Prevents Round 3/4 from overwriting higher priority assignments")
    else:
        print("\n⚠️  Some fixes need further attention")
    
    # Restore original state
    restore_original()
    
    sys.exit(0 if (priority_success and mapping_success) else 1)
